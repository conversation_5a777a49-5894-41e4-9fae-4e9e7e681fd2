/**
 * Twitter Integration Utilities
 * Handles image downloads, clipboard operations, and Twitter Web Intent integration
 */

export interface TwitterShareResult {
  success: boolean;
  message: string;
  imageCopied: boolean;
  imageDownloaded: boolean;
  textCopied: boolean;
  twitterOpened: boolean;
}

/**
 * Downloads an image from a URL to the user's device
 */
export const downloadImage = async (imageUrl: string, filename?: string): Promise<boolean> => {
  try {
    console.log('🔽 Starting image download:', { imageUrl, filename });
    
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }
    
    const blob = await response.blob();
    console.log('📦 Image blob created:', { size: blob.size, type: blob.type });
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `tears-of-the-left-${Date.now()}.png`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Cleanup
    window.URL.revokeObjectURL(url);
    
    console.log('✅ Image download completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Image download failed:', error);
    return false;
  }
};

/**
 * Copies text to clipboard
 */
export const copyTextToClipboard = async (text: string): Promise<boolean> => {
  try {
    console.log('📋 Copying text to clipboard:', { textLength: text.length });
    
    if (navigator.clipboard && window.isSecureContext) {
      // Modern Clipboard API
      await navigator.clipboard.writeText(text);
      console.log('✅ Text copied using Clipboard API');
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (result) {
        console.log('✅ Text copied using execCommand fallback');
        return true;
      } else {
        throw new Error('execCommand copy failed');
      }
    }
  } catch (error) {
    console.error('❌ Text copy failed:', error);
    return false;
  }
};

/**
 * Copies an image to clipboard (modern browsers only)
 */
export const copyImageToClipboard = async (imageUrl: string): Promise<boolean> => {
  try {
    console.log('🖼️ Copying image to clipboard:', { imageUrl });
    
    // Check if Clipboard API is available
    if (!navigator.clipboard || !window.ClipboardItem) {
      console.log('⚠️ Clipboard API not available for images');
      return false;
    }
    
    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }
    
    const blob = await response.blob();
    console.log('📦 Image blob for clipboard:', { size: blob.size, type: blob.type });
    
    // Create ClipboardItem
    const clipboardItem = new ClipboardItem({
      [blob.type]: blob
    });
    
    // Copy to clipboard
    await navigator.clipboard.write([clipboardItem]);
    console.log('✅ Image copied to clipboard successfully');
    return true;
  } catch (error) {
    console.error('❌ Image clipboard copy failed:', error);
    return false;
  }
};

/**
 * Opens Twitter Web Intent with prefilled text
 */
export const openTwitterIntent = (text: string): boolean => {
  try {
    console.log('🐦 Opening Twitter Web Intent:', { textLength: text.length });

    const encodedText = encodeURIComponent(text);
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodedText}`;

    console.log('🔗 Twitter URL:', twitterUrl);

    // Open in the same tab
    window.location.href = twitterUrl;

    console.log('✅ Twitter intent opened successfully');
    return true;
  } catch (error) {
    console.error('❌ Twitter intent failed:', error);
    return false;
  }
};

/**
 * Main function to share image and text to Twitter
 * Attempts multiple strategies with fallbacks
 */
export const shareToTwitter = async (
  imageUrl: string,
  text: string,
  filename?: string
): Promise<TwitterShareResult> => {
  console.log('🚀 Starting Twitter share process:', { imageUrl, text, filename });
  
  const result: TwitterShareResult = {
    success: false,
    message: '',
    imageCopied: false,
    imageDownloaded: false,
    textCopied: false,
    twitterOpened: false
  };
  
  try {
    // Step 1: Try to copy image to clipboard
    console.log('📋 Step 1: Attempting image clipboard copy...');
    result.imageCopied = await copyImageToClipboard(imageUrl);
    
    // Step 2: Download image as backup
    console.log('💾 Step 2: Downloading image as backup...');
    result.imageDownloaded = await downloadImage(imageUrl, filename);
    
    // Step 3: Copy text to clipboard (if image copy failed)
    if (!result.imageCopied) {
      console.log('📝 Step 3: Copying text to clipboard as fallback...');
      result.textCopied = await copyTextToClipboard(text);
    }
    
    // Step 4: Open Twitter Web Intent
    console.log('🐦 Step 4: Opening Twitter Web Intent...');
    result.twitterOpened = openTwitterIntent(text);
    
    // Determine success and message
    if (result.imageCopied && result.twitterOpened) {
      result.success = true;
      result.message = 'Image copied to clipboard! Twitter opened with your text. Just paste the image in your tweet.';
    } else if (result.imageDownloaded && result.twitterOpened) {
      result.success = true;
      if (result.textCopied) {
        result.message = 'Image downloaded and text copied! Twitter opened. Upload the downloaded image to your tweet.';
      } else {
        result.message = 'Image downloaded! Twitter opened with your text. Upload the downloaded image to your tweet.';
      }
    } else if (result.twitterOpened) {
      result.success = true;
      result.message = 'Twitter opened with your text. Please manually save and upload the image.';
    } else {
      result.success = false;
      result.message = 'Unable to open Twitter. Please copy your text and image manually.';
    }
    
    console.log('🎯 Twitter share result:', result);
    return result;
    
  } catch (error) {
    console.error('💥 Twitter share process failed:', error);
    result.success = false;
    result.message = 'An error occurred while sharing to Twitter. Please try again.';
    return result;
  }
};

/**
 * Validates tweet text length (Twitter's limit is 280 characters)
 */
export const validateTweetText = (text: string): { isValid: boolean; remainingChars: number } => {
  const maxLength = 280;
  const remainingChars = maxLength - text.length;
  
  return {
    isValid: remainingChars >= 0,
    remainingChars
  };
};

/**
 * Generates suggested hashtags for the image
 */
export const getSuggestedHashtags = (): string[] => {
  return [
    '#TearsOfTheLeft',
    '#AIArt',
    '#ImageTransformation',
    '#DigitalArt',
    '#CreativeAI'
  ];
};

/**
 * Creates a default tweet text with @CheersToTears mention
 */
export const createDefaultTweetText = (): string => {
  return `Add your post text here!

Made with @CheersToTears`;
};
