import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import Image from "next/image";

export default async function Home() {
  // Check if user is already authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // If authenticated, redirect to editor
  if (user) {
    redirect("/editor");
  }

  return (
    <main className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 fade-in">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex justify-center mb-4">
            <div className="w-24 h-24 relative group">
              <Image
                src="/logo.svg"
                alt="Tears of the Left Logo"
                width={96}
                height={96}
                className="w-full h-full object-contain animate-pulse-slow"
              />
              {/* Clockwise Stars Animation */}
              <div className="absolute inset-0 w-full h-full">
                {/* Star 1 - 12 o'clock */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '0s'}}></div>
                {/* Star 2 - 1:30 o'clock */}
                <div className="absolute top-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '0.3s'}}></div>
                {/* Star 3 - 3 o'clock */}
                <div className="absolute top-1/2 right-0 transform translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '0.6s'}}></div>
                {/* Star 4 - 4:30 o'clock */}
                <div className="absolute bottom-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '0.9s'}}></div>
                {/* Star 5 - 6 o'clock */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '1.2s'}}></div>
                {/* Star 6 - 7:30 o'clock */}
                <div className="absolute bottom-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '1.5s'}}></div>
                {/* Star 7 - 9 o'clock */}
                <div className="absolute top-1/2 left-0 transform -translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '1.8s'}}></div>
                {/* Star 8 - 10:30 o'clock */}
                <div className="absolute top-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0" style={{animationDelay: '2.1s'}}></div>
              </div>
            </div>
          </div>
          <h1 className="text-4xl font-bold text-foreground">
            Tears of the Left
          </h1>
          <p className="text-secondary-foreground">
            Transform your images with AI
          </p>
        </div>

        {/* Auth Forms */}
        <div className="space-y-6">
          <AuthTabs />
        </div>

        {/* Footer */}
        <div className="flex items-center justify-center">
          <ThemeSwitcher />
        </div>
      </div>
    </main>
  );
}

function AuthTabs() {
  return (
    <div className="space-y-4">
      <Card className="border-accent/30 bg-card/90 shadow-xl backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-center text-xl font-bold text-foreground">Welcome</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <Button asChild variant="outline" className="h-12 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent text-foreground font-semibold transition-all duration-200 hover:scale-105">
              <a href="/auth/login">Sign In</a>
            </Button>
            <Button asChild className="h-12 bg-primary hover:bg-primary/80 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 transform">
              <a href="/auth/sign-up">Sign Up</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
