(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[766],{3999:(e,t,a)=>{"use strict";a.d(t,{cn:()=>o});var s=a(2596),r=a(9688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}a(7358).env.SUPABASE_SERVICE_KEY},6414:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(3865);function r(){return(0,s.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},6928:(e,t,a)=>{Promise.resolve().then(a.bind(a,7263))},7168:(e,t,a)=>{"use strict";a.d(t,{$:()=>c});var s=a(5155),r=a(2115),o=a(9708),l=a(2085),n=a(3999);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:a,variant:r,size:l,asChild:c=!1,...d}=e,m=c?o.DX:"button";return(0,s.jsx)(m,{className:(0,n.cn)(i({variant:r,size:l,className:a})),ref:t,...d})});c.displayName="Button"},7263:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(5155),r=a(2115),o=a(7168),l=a(8482),n=a(4835),i=a(9869),c=a(3311),d=a(1788),m=a(5695),u=a(6414),h=a(6766),x=a(3999);function g(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,x.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}var p=a(556),f=a(4357),b=a(1154),w=a(2486),y=a(6671);let v=async(e,t)=>{try{console.log("\uD83D\uDD3D Starting image download:",{imageUrl:e,filename:t});let a=await fetch(e);if(!a.ok)throw Error("Failed to fetch image: ".concat(a.status," ").concat(a.statusText));let s=await a.blob();console.log("\uD83D\uDCE6 Image blob created:",{size:s.size,type:s.type});let r=window.URL.createObjectURL(s),o=document.createElement("a");return o.href=r,o.download=t||"tears-of-the-left-".concat(Date.now(),".png"),document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(r),console.log("✅ Image download completed successfully"),!0}catch(e){return console.error("❌ Image download failed:",e),!1}},j=async e=>{try{if(console.log("\uD83D\uDCCB Copying text to clipboard:",{textLength:e.length}),navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),console.log("✅ Text copied using Clipboard API"),!0;{let t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let a=document.execCommand("copy");if(document.body.removeChild(t),a)return console.log("✅ Text copied using execCommand fallback"),!0;throw Error("execCommand copy failed")}}catch(e){return console.error("❌ Text copy failed:",e),!1}},N=async e=>{try{if(console.log("\uD83D\uDDBC️ Copying image to clipboard:",{imageUrl:e}),!navigator.clipboard||!window.ClipboardItem)return console.log("⚠️ Clipboard API not available for images"),!1;let t=await fetch(e);if(!t.ok)throw Error("Failed to fetch image: ".concat(t.status));let a=await t.blob();console.log("\uD83D\uDCE6 Image blob for clipboard:",{size:a.size,type:a.type});let s=new ClipboardItem({[a.type]:a});return await navigator.clipboard.write([s]),console.log("✅ Image copied to clipboard successfully"),!0}catch(e){return console.error("❌ Image clipboard copy failed:",e),!1}},C=async(e,t,a)=>{console.log("\uD83D\uDE80 Starting Twitter share process:",{imageUrl:e,text:t,filename:a});let s={success:!1,message:"",imageCopied:!1,imageDownloaded:!1,textCopied:!1,twitterOpened:!1};try{return console.log("\uD83D\uDCCB Step 1: Attempting image clipboard copy..."),s.imageCopied=await N(e),console.log("\uD83D\uDCBE Step 2: Downloading image as backup..."),s.imageDownloaded=await v(e,a),s.imageCopied||(console.log("\uD83D\uDCDD Step 3: Copying text to clipboard as fallback..."),s.textCopied=await j(t)),console.log("\uD83D\uDC26 Step 4: Opening Twitter Web Intent..."),s.twitterOpened=(e=>{try{console.log("\uD83D\uDC26 Opening Twitter Web Intent:",{textLength:e.length});let t=encodeURIComponent(e),a="https://twitter.com/intent/tweet?text=".concat(t);return console.log("\uD83D\uDD17 Twitter URL:",a),window.location.href=a,console.log("✅ Twitter intent opened successfully"),!0}catch(e){return console.error("❌ Twitter intent failed:",e),!1}})(t),s.imageCopied&&s.twitterOpened?(s.success=!0,s.message="Image copied to clipboard! Twitter opened with your text. Just paste the image in your tweet."):s.imageDownloaded&&s.twitterOpened?(s.success=!0,s.textCopied?s.message="Image downloaded and text copied! Twitter opened. Upload the downloaded image to your tweet.":s.message="Image downloaded! Twitter opened with your text. Upload the downloaded image to your tweet."):s.twitterOpened?(s.success=!0,s.message="Twitter opened with your text. Please manually save and upload the image."):(s.success=!1,s.message="Unable to open Twitter. Please copy your text and image manually."),console.log("\uD83C\uDFAF Twitter share result:",s),s}catch(e){return console.error("\uD83D\uDCA5 Twitter share process failed:",e),s.success=!1,s.message="An error occurred while sharing to Twitter. Please try again.",s}};function T(e){let{imageUrl:t,className:a}=e,[n,i]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{i("Add your post text here!\n\nMade with @CheersToTears")},[]);let m=async()=>{if(console.log("\uD83D\uDC26 TwitterPostSection: Starting share process"),!n.trim())return void y.oR.error("Please enter some text for your tweet.");d(!0);try{let e=await C(t,n,"tears-of-the-left-".concat(Date.now(),".png"));e.success?y.oR.success(e.message,{duration:5e3,action:e.imageCopied?{label:"Got it!",onClick:()=>y.oR.dismiss()}:void 0}):y.oR.error(e.message,{duration:7e3,action:{label:"Retry",onClick:()=>m()}})}catch(e){console.error("❌ TwitterPostSection: Share failed:",e),y.oR.error("Failed to share to Twitter. Please try again.")}finally{d(!1)}},u=async()=>{try{await navigator.clipboard.writeText(n),y.oR.success("Tweet text copied to clipboard!")}catch(e){console.error("Failed to copy text:",e),y.oR.error("Failed to copy text to clipboard.")}};return(0,s.jsxs)(l.Zp,{className:"border-accent/30 bg-secondary/5 shadow-xl ".concat(a),children:[(0,s.jsx)(l.aR,{className:"pb-4",children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-accent"}),"Share to Twitter"]})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{htmlFor:"tweet-text",className:"text-sm font-medium text-foreground",children:"Tweet Text"}),(0,s.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:u,className:"h-6 px-2 text-xs",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Copy"]})]}),(0,s.jsx)(g,{id:"tweet-text",value:n,onChange:e=>i(e.target.value),placeholder:"What's happening?",className:"min-h-[100px] resize-none",maxLength:300})]}),(0,s.jsxs)("div",{className:"text-xs text-white p-3 bg-accent/5 rounded-lg border border-accent/20",children:[(0,s.jsx)("p",{className:"font-medium mb-1 text-white",children:"How it works:"}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-white",children:[(0,s.jsx)("li",{children:"Your image will be copied to clipboard (if supported)"}),(0,s.jsx)("li",{children:"Image will be downloaded as backup"}),(0,s.jsx)("li",{children:"Twitter will open with your text pre-filled"}),(0,s.jsx)("li",{children:"Paste or upload the image in your tweet"})]})]}),(0,s.jsx)(o.$,{onClick:m,disabled:c||!n.trim(),className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100 h-14",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"h-5 w-5 mr-3 animate-spin"}),"Sharing to Twitter..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.A,{className:"h-5 w-5 mr-3"}),"Share to Twitter"]})})]})]})}function k(){let[e,t]=(0,r.useState)(null),[a,x]=(0,r.useState)(!1),[g,p]=(0,r.useState)(null),[f,b]=(0,r.useState)(null),[w,v]=(0,r.useState)(null),[j,N]=(0,r.useState)(!1),C=(0,m.useRouter)(),k=(0,u.U)(),I=async()=>{await k.auth.signOut(),C.push("/")},S=async e=>{if(t(e),p(null),b(null),v(null),e)try{let t=new FormData;t.append("file",e);let a=await fetch("/api/upload",{method:"POST",body:t});if(!a.ok){let e=await a.json();throw Error(e.error||"Upload failed")}let s=await a.json();v(s.image)}catch(e){p(e instanceof Error?e.message:"Upload failed")}},D=async()=>{if(!w)return void p("Please upload an image first");x(!0),p(null),b(null);try{let e=await fetch("/api/process",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({imageData:w,prompt:"Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution"})});if(!e.ok){let t=await e.json();throw Error(t.error||"Processing failed")}let t=await e.json();b(t)}catch(e){p(e instanceof Error?e.message:"Processing failed")}finally{x(!1)}},R=e&&!a;return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("div",{className:"sticky top-0 z-50 w-full border-b border-accent/20 bg-background/80 backdrop-blur-sm",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 relative",children:(0,s.jsx)(h.default,{src:"/logo.svg",alt:"Tears of the Left Logo",width:40,height:40,className:"w-full h-full object-contain"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Tears of the Left"})]}),(0,s.jsxs)(o.$,{onClick:I,variant:"outline",size:"sm",className:"flex items-center gap-2 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent transition-all duration-200",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),"Sign Out"]})]})}),(0,s.jsx)("div",{className:"w-full px-6 py-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8 fade-in",children:[(0,s.jsx)(l.Zp,{className:"border-2 border-dashed transition-all duration-300 hover:shadow-lg ".concat(j?"border-accent bg-accent/10 shadow-lg scale-[1.02]":"border-secondary/30 bg-secondary/5"),onDragOver:e=>{e.preventDefault(),N(!0)},onDragLeave:e=>{e.preventDefault(),N(!1)},onDrop:e=>{e.preventDefault(),N(!1);let t=e.dataTransfer.files;if(t.length>0){let e=t[0];e.type.startsWith("image/")?S(e):p("Please drop an image file")}},children:(0,s.jsx)(l.Wu,{className:"p-8",children:e?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"h-6 w-6 text-accent"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold text-foreground text-base",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-foreground/70",children:[(e.size/1024/1024).toFixed(2)," MB • Ready to transform"]})]})]}),(0,s.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>S(null),disabled:a,className:"border-accent/30 hover:bg-accent/10 text-foreground",children:"Change"})]}),(0,s.jsx)(o.$,{onClick:D,disabled:!R,className:"w-full h-14 text-lg bg-primary hover:bg-primary/90 text-primary-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",size:"lg",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3"}),"Creating tears..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-6 w-6 mr-3"}),"Transform Image"]})})]}):(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsx)("div",{className:"mx-auto w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110",children:(0,s.jsx)(i.A,{className:"h-10 w-10 transition-colors ".concat(j?"text-accent":"text-muted-foreground")})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-foreground",children:"Upload your image"}),(0,s.jsx)("p",{className:"text-foreground/80 text-base",children:j?"✨ Drop your image here":'Transform with the "Tears of the Left" effect'})]}),(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;return S((null==(t=e.target.files)?void 0:t[0])||null)},className:"hidden",id:"file-upload",disabled:a}),(0,s.jsx)(o.$,{asChild:!0,className:"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200",disabled:a,children:(0,s.jsx)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:"Choose Image"})})]})})}),g&&(0,s.jsx)(l.Zp,{className:"border-destructive/50 bg-destructive/10 shadow-lg",children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 text-center justify-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-destructive/20 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-destructive font-bold",children:"!"})}),(0,s.jsx)("p",{className:"text-destructive font-medium text-base",children:g})]})})}),a&&(0,s.jsx)(l.Zp,{className:"border-accent/30 bg-secondary/5 shadow-xl",children:(0,s.jsx)(l.Wu,{className:"p-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-3",children:"Creating your masterpiece..."}),(0,s.jsx)("p",{className:"text-base text-foreground/80",children:"The AI is painting tears of emotion onto your image"})]}),(0,s.jsx)("div",{className:"relative rounded-xl overflow-hidden bg-accent/5 h-96 flex items-center justify-center border border-accent/20",children:(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"w-20 h-20 mx-auto relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-accent/30 animate-ping opacity-75"}),(0,s.jsx)("div",{className:"absolute inset-2 rounded-full bg-accent/50 animate-pulse"}),(0,s.jsx)("div",{className:"absolute inset-4 rounded-full bg-accent"})]}),(0,s.jsxs)("div",{className:"absolute -left-10 top-10 space-y-3",children:[(0,s.jsx)("div",{className:"w-3 h-4 bg-accent rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,s.jsx)("div",{className:"w-3 h-4 bg-accent/70 rounded-full animate-bounce opacity-70",style:{animationDelay:"0.2s"}}),(0,s.jsx)("div",{className:"w-3 h-4 bg-accent/50 rounded-full animate-bounce opacity-50",style:{animationDelay:"0.4s"}})]}),(0,s.jsxs)("div",{className:"absolute -right-10 top-10 space-y-3",children:[(0,s.jsx)("div",{className:"w-3 h-4 bg-accent/60 rounded-full animate-bounce",style:{animationDelay:"0.3s"}}),(0,s.jsx)("div",{className:"w-3 h-4 bg-accent/40 rounded-full animate-bounce opacity-60",style:{animationDelay:"0.5s"}})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-lg font-semibold text-foreground",children:"Transforming with emotion..."}),(0,s.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,s.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})})}),f&&!a&&(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-3",children:"✨ Your transformed image"}),(0,s.jsx)("p",{className:"text-base text-foreground/80",children:"The “Tears of the Left” effect has been applied"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:items-start",children:[(0,s.jsx)(l.Zp,{className:"border-accent/30 bg-secondary/5 shadow-xl h-full",children:(0,s.jsx)(l.Wu,{className:"p-8 h-full flex flex-col",children:(0,s.jsxs)("div",{className:"space-y-6 flex-1 flex flex-col",children:[(0,s.jsx)("div",{className:"relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-4 flex-1 flex items-center justify-center",children:(0,s.jsx)(h.default,{src:f.editedImageUrl,alt:"Transformed image",width:1024,height:1024,className:"w-full h-auto max-h-96 object-contain mx-auto rounded-lg shadow-lg",unoptimized:!0})}),(0,s.jsxs)(o.$,{onClick:()=>{let e=document.createElement("a");e.href=f.editedImageUrl,e.download="tears-of-the-left-".concat(Date.now(),".png"),document.body.appendChild(e),e.click(),document.body.removeChild(e)},className:"w-full h-14 bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-3"}),"Download Image"]})]})})}),(0,s.jsx)(T,{imageUrl:f.editedImageUrl,className:"h-full"})]})]})]})}),(0,s.jsx)(y.l$,{position:"top-right",richColors:!0})]})}},8482:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>n});var s=a(5155),r=a(2115),o=a(3999);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...r})});l.displayName="Card";let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",a),...r})});n.displayName="CardHeader";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",a),...r})});i.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter"}},e=>{e.O(0,[787,865,232,939,441,964,358],()=>e(e.s=6928)),_N_E=e.O()}]);