{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_35c8d03a._.js", "server/edge/chunks/[root-of-the-server]__b8e15c56._.js", "server/edge/chunks/edge-wrapper_6068de0d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UiH8fJZxArHLaH6Q65VYNDLXP79LSp17ZAoOb5d86m0=", "__NEXT_PREVIEW_MODE_ID": "5d3ed072de014441985c3c266758da18", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8086f46ba7ee07d47b8ee26e7c656f1fb849f39be59c2beb3bd15e4d08ea5502", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73d68587c24a7cf3e7aa2da6d679789a2b0f7218aae123bf4a714aae9405c69e"}}}, "instrumentation": null, "functions": {}}