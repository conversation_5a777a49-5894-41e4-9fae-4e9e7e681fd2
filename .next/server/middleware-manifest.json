{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "gocQFHrYKKuEmV9UERSoS", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UiH8fJZxArHLaH6Q65VYNDLXP79LSp17ZAoOb5d86m0=", "__NEXT_PREVIEW_MODE_ID": "404c072f9dcea372851a3b6f7b590b62", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ff4c78ef88e16a31536e51d4b9f1465c9bf9b56442163b793d2d1336eaeb6e1d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0b7f607429c7fd9a9a2f1b102e8467167bbff60f81da80da140bc9ad31eaf6ab"}}}, "functions": {}, "sortedMiddleware": ["/"]}