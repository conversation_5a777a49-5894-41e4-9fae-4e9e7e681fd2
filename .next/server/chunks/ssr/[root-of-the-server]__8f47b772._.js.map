{"version": 3, "sources": [], "sections": [{"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\nimport { cookies } from \"next/headers\";\n\n/**\n * Especially important if using Fluid compute: Don't put this client in a\n * global variable. Always create a new client within each function when using\n * it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies();\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll();\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options),\n            );\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    },\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAOO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/layout.tsx"], "sourcesContent": ["import { redirect } from \"next/navigation\";\nimport { createClient } from \"@/lib/supabase/server\";\n\nexport default async function EditorLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const supabase = await createClient();\n\n  const { data, error } = await supabase.auth.getClaims();\n  if (error || !data?.claims) {\n    redirect(\"/auth/login\");\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAEe,eAAe,aAAa,EACzC,QAAQ,EAGT;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS;IACrD,IAAI,SAAS,CAAC,MAAM,QAAQ;QAC1B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}]}