{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\nimport { cookies } from \"next/headers\";\n\n/**\n * Especially important if using Fluid compute: Don't put this client in a\n * global variable. Always create a new client within each function when using\n * it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies();\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll();\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options),\n            );\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    },\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAOO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Environment variable checks\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n\nexport const hasRateLimitingEnvVars =\n  hasEnvVars &&\n  process.env.SUPABASE_SERVICE_KEY;\n\nexport function validateRateLimitingEnvironment(): void {\n  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');\n  }\n  if (!process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY environment variable');\n  }\n  if (!process.env.SUPABASE_SERVICE_KEY) {\n    throw new Error('Missing SUPABASE_SERVICE_KEY environment variable - required for rate limiting');\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,aACX;AAGK,MAAM,yBACX,cACA,QAAQ,GAAG,CAAC,oBAAoB;AAE3B,SAAS;IACd;;IAGA;;IAGA,IAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB,EAAE;QACrC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/rateLimiter.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { validateRateLimitingEnvironment } from './utils';\n\n// Types for rate limiting responses\nexport interface RateLimitStatus {\n  allowed: boolean;\n  userRemaining: number;\n  userLimit: number;\n  globalRemaining: number;\n  globalLimit: number;\n  resetTime: string;\n  reason?: string;\n}\n\nexport interface GenerationLogEntry {\n  userId: number;\n  prompt: string;\n  success: boolean;\n  imageUrl?: string;\n  errorMessage?: string;\n}\n\n// Create service-level Supabase client for rate limiting operations\nfunction createServiceClient() {\n  // Validate all required environment variables are present\n  validateRateLimitingEnvironment();\n\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\n  const serviceKey = process.env.SUPABASE_SERVICE_KEY!;\n\n  return createClient(supabaseUrl, serviceKey, {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  });\n}\n\n/**\n * Maps a Supabase UUID to a numeric user ID for rate limiting\n */\nexport async function getUserNumericId(supabaseUuid: string): Promise<number> {\n  console.log('🔄 Mapping Supabase UUID to numeric ID:', supabaseUuid);\n  \n  try {\n    const supabase = createServiceClient();\n    \n    const { data, error } = await supabase.rpc('get_or_create_numeric_user_id', {\n      supabase_uuid: supabaseUuid\n    });\n\n    if (error) {\n      console.error('❌ Error mapping user ID:', error);\n      throw new Error(`Failed to map user ID: ${error.message}`);\n    }\n\n    console.log('✅ Mapped to numeric ID:', data);\n    return data;\n  } catch (error) {\n    console.error('❌ Critical error in getUserNumericId:', error);\n    throw error;\n  }\n}\n\n/**\n * Checks if generation is allowed for a user (checks both user and global limits)\n */\nexport async function checkGenerationAllowed(userId: number): Promise<RateLimitStatus> {\n  console.log('🔍 Checking generation limits for user:', userId);\n  \n  try {\n    const supabase = createServiceClient();\n    \n    const { data, error } = await supabase.rpc('check_generation_allowed', {\n      user_id_param: userId\n    });\n\n    if (error) {\n      console.error('❌ Error checking generation limits:', error);\n      throw new Error(`Failed to check limits: ${error.message}`);\n    }\n\n    console.log('📊 Rate limit status:', data);\n\n    const status: RateLimitStatus = {\n      allowed: data.allowed,\n      userRemaining: data.user_remaining,\n      userLimit: data.user_limit,\n      globalRemaining: data.global_remaining,\n      globalLimit: data.global_limit,\n      resetTime: 'midnight UTC',\n      reason: data.allowed ? undefined : (\n        data.user_remaining <= 0 ? 'User daily limit exceeded' : 'Global daily limit exceeded'\n      )\n    };\n\n    return status;\n  } catch (error) {\n    console.error('❌ Critical error in checkGenerationAllowed:', error);\n    throw error;\n  }\n}\n\n/**\n * Decrements both user and global limits atomically\n */\nexport async function decrementLimits(userId: number): Promise<boolean> {\n  console.log('⬇️ Decrementing limits for user:', userId);\n  \n  try {\n    const supabase = createServiceClient();\n    \n    const { data, error } = await supabase.rpc('decrement_both_limits', {\n      user_id_param: userId\n    });\n\n    if (error) {\n      console.error('❌ Error decrementing limits:', error);\n      throw new Error(`Failed to decrement limits: ${error.message}`);\n    }\n\n    console.log('✅ Limits decremented successfully:', data);\n    return data;\n  } catch (error) {\n    console.error('❌ Critical error in decrementLimits:', error);\n    throw error;\n  }\n}\n\n/**\n * Logs a generation attempt for audit and analytics\n */\nexport async function logGenerationAttempt(entry: GenerationLogEntry): Promise<void> {\n  console.log('📝 Logging generation attempt for user:', entry.userId);\n  \n  try {\n    const supabase = createServiceClient();\n    \n    const { error } = await supabase\n      .from('cry_generation_logs')\n      .insert({\n        user_id: entry.userId,\n        prompt: entry.prompt.substring(0, 1000), // Truncate very long prompts\n        success: entry.success,\n        image_url: entry.imageUrl,\n        error_message: entry.errorMessage\n      });\n\n    if (error) {\n      console.error('❌ Error logging generation attempt:', error);\n      // Don't throw here - logging failures shouldn't break the main flow\n    } else {\n      console.log('✅ Generation attempt logged successfully');\n    }\n  } catch (error) {\n    console.error('❌ Critical error in logGenerationAttempt:', error);\n    // Don't throw here - logging failures shouldn't break the main flow\n  }\n}\n\n/**\n * Gets current rate limit status for a user (for display purposes)\n */\nexport async function getRateLimitStatus(userId: number): Promise<RateLimitStatus> {\n  return checkGenerationAllowed(userId);\n}\n\n/**\n * Main rate limiting function that combines all checks\n * Returns true if generation should proceed, false otherwise\n */\nexport async function enforceRateLimit(supabaseUuid: string): Promise<{\n  allowed: boolean;\n  status: RateLimitStatus;\n  userId: number;\n}> {\n  console.log('🛡️ Enforcing rate limit for UUID:', supabaseUuid);\n  \n  try {\n    // Step 1: Map UUID to numeric ID\n    const userId = await getUserNumericId(supabaseUuid);\n    \n    // Step 2: Check if generation is allowed\n    const status = await checkGenerationAllowed(userId);\n    \n    console.log('🛡️ Rate limit enforcement result:', {\n      allowed: status.allowed,\n      reason: status.reason,\n      userRemaining: status.userRemaining,\n      globalRemaining: status.globalRemaining\n    });\n    \n    return {\n      allowed: status.allowed,\n      status,\n      userId\n    };\n  } catch (error) {\n    console.error('❌ Critical error in enforceRateLimit:', error);\n    \n    // In case of database errors, we fail closed (deny the request)\n    // This is safer than allowing unlimited requests\n    return {\n      allowed: false,\n      status: {\n        allowed: false,\n        userRemaining: 0,\n        userLimit: 0,\n        globalRemaining: 0,\n        globalLimit: 0,\n        resetTime: 'midnight UTC',\n        reason: 'Rate limiting system temporarily unavailable'\n      },\n      userId: 0\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAqBA,oEAAoE;AACpE,SAAS;IACP,0DAA0D;IAC1D,CAAA,GAAA,8GAAA,CAAA,kCAA+B,AAAD;IAE9B,MAAM;IACN,MAAM,aAAa,QAAQ,GAAG,CAAC,oBAAoB;IAEnD,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,YAAY;QAC3C,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF;AAKO,eAAe,iBAAiB,YAAoB;IACzD,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,iCAAiC;YAC1E,eAAe;QACjB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QAC3D;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,MAAc;IACzD,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,4BAA4B;YACrE,eAAe;QACjB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D;QAEA,QAAQ,GAAG,CAAC,yBAAyB;QAErC,MAAM,SAA0B;YAC9B,SAAS,KAAK,OAAO;YACrB,eAAe,KAAK,cAAc;YAClC,WAAW,KAAK,UAAU;YAC1B,iBAAiB,KAAK,gBAAgB;YACtC,aAAa,KAAK,YAAY;YAC9B,WAAW;YACX,QAAQ,KAAK,OAAO,GAAG,YACrB,KAAK,cAAc,IAAI,IAAI,8BAA8B;QAE7D;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,MAAM;IACR;AACF;AAKO,eAAe,gBAAgB,MAAc;IAClD,QAAQ,GAAG,CAAC,oCAAoC;IAEhD,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC,yBAAyB;YAClE,eAAe;QACjB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,MAAM,OAAO,EAAE;QAChE;QAEA,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;AACF;AAKO,eAAe,qBAAqB,KAAyB;IAClE,QAAQ,GAAG,CAAC,2CAA2C,MAAM,MAAM;IAEnE,IAAI;QACF,MAAM,WAAW;QAEjB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;YACN,SAAS,MAAM,MAAM;YACrB,QAAQ,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG;YAClC,SAAS,MAAM,OAAO;YACtB,WAAW,MAAM,QAAQ;YACzB,eAAe,MAAM,YAAY;QACnC;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,oEAAoE;QACtE,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC3D,oEAAoE;IACtE;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,OAAO,uBAAuB;AAChC;AAMO,eAAe,iBAAiB,YAAoB;IAKzD,QAAQ,GAAG,CAAC,sCAAsC;IAElD,IAAI;QACF,iCAAiC;QACjC,MAAM,SAAS,MAAM,iBAAiB;QAEtC,yCAAyC;QACzC,MAAM,SAAS,MAAM,uBAAuB;QAE5C,QAAQ,GAAG,CAAC,sCAAsC;YAChD,SAAS,OAAO,OAAO;YACvB,QAAQ,OAAO,MAAM;YACrB,eAAe,OAAO,aAAa;YACnC,iBAAiB,OAAO,eAAe;QACzC;QAEA,OAAO;YACL,SAAS,OAAO,OAAO;YACvB;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QAEvD,gEAAgE;QAChE,iDAAiD;QACjD,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,SAAS;gBACT,eAAe;gBACf,WAAW;gBACX,iBAAiB;gBACjB,aAAa;gBACb,WAAW;gBACX,QAAQ;YACV;YACA,QAAQ;QACV;IACF;AACF", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/imageUtils.ts"], "sourcesContent": ["import sharp from 'sharp';\nimport fs from 'fs';\n\n/**\n * Converts SVG to PNG buffer with specified dimensions\n */\nexport async function svgToPng(svgPath: string, width: number, height: number): Promise<Buffer> {\n  try {\n    console.log(`Converting SVG to PNG: ${svgPath}, dimensions: ${width}x${height}`);\n    \n    // Read SVG file\n    const svgBuffer = await fs.promises.readFile(svgPath);\n    \n    // Convert SVG to PNG using Sharp\n    const pngBuffer = await sharp(svgBuffer)\n      .resize(width, height, {\n        fit: 'contain',\n        background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background\n      })\n      .png()\n      .toBuffer();\n    \n    console.log(`SVG converted to PNG successfully. Output size: ${pngBuffer.length} bytes`);\n    return pngBuffer;\n  } catch (error) {\n    console.error('Error converting SVG to PNG:', error);\n    throw new Error(`Failed to convert SVG to PNG: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Adds logo overlay to the bottom right corner of an image\n */\nexport async function addLogoOverlay(\n  imageBuffer: Buffer,\n  logoPath: string,\n  options: {\n    logoSizePercent?: number; // Logo size as percentage of image width\n    paddingPercent?: number;  // Padding as percentage of image width\n    opacity?: number;         // Logo opacity (0-1)\n  } = {}\n): Promise<Buffer> {\n  try {\n    const {\n      logoSizePercent = 12,  // 12% of image width\n      paddingPercent = 3,    // 3% padding from edges\n      opacity = 0.9          // 90% opacity\n    } = options;\n\n    console.log('Starting logo overlay process with options:', {\n      logoSizePercent,\n      paddingPercent,\n      opacity,\n      logoPath\n    });\n\n    // Get image metadata\n    const imageMetadata = await sharp(imageBuffer).metadata();\n    const imageWidth = imageMetadata.width || 1024;\n    const imageHeight = imageMetadata.height || 1024;\n    \n    console.log(`Image dimensions: ${imageWidth}x${imageHeight}`);\n\n    // Calculate logo dimensions\n    const logoSize = Math.round(imageWidth * (logoSizePercent / 100));\n    const padding = Math.round(imageWidth * (paddingPercent / 100));\n    \n    console.log(`Logo size: ${logoSize}px, Padding: ${padding}px`);\n\n    // Convert SVG logo to PNG with calculated dimensions\n    const logoPngBuffer = await svgToPng(logoPath, logoSize, logoSize);\n\n    // Calculate logo position (bottom right with padding)\n    const logoLeft = imageWidth - logoSize - padding;\n    const logoTop = imageHeight - logoSize - padding;\n    \n    console.log(`Logo position: left=${logoLeft}, top=${logoTop}`);\n\n    // Create logo overlay with opacity\n    const { data: logoData, info: logoInfo } = await sharp(logoPngBuffer)\n      .ensureAlpha()\n      .raw()\n      .toBuffer({ resolveWithObject: true });\n\n    for (let i = 3; i < logoData.length; i += 4) {\n      logoData[i] = Math.round(logoData[i] * opacity);\n    }\n\n    const logoOverlay = await sharp(logoData, { raw: logoInfo }).png().toBuffer();\n\n    // Composite the logo onto the image\n    const resultBuffer = await sharp(imageBuffer)\n      .composite([{\n        input: logoOverlay,\n        left: logoLeft,\n        top: logoTop,\n        blend: 'over'\n      }])\n      .png()\n      .toBuffer();\n\n    console.log(`Logo overlay completed successfully. Result size: ${resultBuffer.length} bytes`);\n    return resultBuffer;\n\n  } catch (error) {\n    console.error('Error adding logo overlay:', error);\n    throw new Error(`Failed to add logo overlay: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Processes a base64 image string by adding logo overlay\n */\nexport async function processImageWithLogo(\n  base64Image: string,\n  logoPath: string,\n  options?: {\n    logoSizePercent?: number;\n    paddingPercent?: number;\n    opacity?: number;\n  }\n): Promise<string> {\n  try {\n    console.log('Processing base64 image with logo overlay');\n    \n    // Extract base64 data (remove data URL prefix if present)\n    const base64Data = base64Image.replace(/^data:image\\/[a-z]+;base64,/, '');\n    const imageBuffer = Buffer.from(base64Data, 'base64');\n    \n    console.log(`Input image buffer size: ${imageBuffer.length} bytes`);\n\n    // Add logo overlay\n    const processedBuffer = await addLogoOverlay(imageBuffer, logoPath, options);\n\n    // Convert back to base64 data URL\n    const resultBase64 = `data:image/png;base64,${processedBuffer.toString('base64')}`;\n    \n    console.log('Image processing with logo completed successfully');\n    return resultBase64;\n\n  } catch (error) {\n    console.error('Error processing image with logo:', error);\n    throw new Error(`Failed to process image with logo: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n/**\n * Optimizes image buffer with specified options\n */\nexport async function optimizeImage(\n  buffer: Buffer,\n  options: {\n    maxWidth?: number;\n    maxHeight?: number;\n    quality?: number;\n    format?: 'jpeg' | 'png' | 'webp';\n  } = {}\n): Promise<Buffer> {\n  const {\n    maxWidth = 1024,\n    maxHeight = 1024,\n    quality = 85,\n    format = 'jpeg'\n  } = options;\n\n  console.log(`Optimizing image with options:`, { maxWidth, maxHeight, quality, format });\n\n  let pipeline = sharp(buffer)\n    .resize(maxWidth, maxHeight, {\n      fit: 'inside',\n      withoutEnlargement: true\n    });\n\n  switch (format) {\n    case 'jpeg':\n      pipeline = pipeline.jpeg({ quality });\n      break;\n    case 'png':\n      pipeline = pipeline.png({ compressionLevel: 8 });\n      break;\n    case 'webp':\n      pipeline = pipeline.webp({ quality });\n      break;\n  }\n\n  const result = await pipeline.toBuffer();\n  console.log(`Image optimization completed. Output size: ${result.length} bytes`);\n  \n  return result;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAKO,eAAe,SAAS,OAAe,EAAE,KAAa,EAAE,MAAc;IAC3E,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,EAAE,MAAM,CAAC,EAAE,QAAQ;QAE/E,gBAAgB;QAChB,MAAM,YAAY,MAAM,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAE7C,iCAAiC;QACjC,MAAM,YAAY,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,WAC3B,MAAM,CAAC,OAAO,QAAQ;YACrB,KAAK;YACL,YAAY;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,OAAO;YAAE,EAAE,yBAAyB;QACtE,GACC,GAAG,GACH,QAAQ;QAEX,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,UAAU,MAAM,CAAC,MAAM,CAAC;QACvF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC7G;AACF;AAKO,eAAe,eACpB,WAAmB,EACnB,QAAgB,EAChB,UAII,CAAC,CAAC;IAEN,IAAI;QACF,MAAM,EACJ,kBAAkB,EAAE,EACpB,iBAAiB,CAAC,EAClB,UAAU,IAAa,cAAc;QAAxB,EACd,GAAG;QAEJ,QAAQ,GAAG,CAAC,+CAA+C;YACzD;YACA;YACA;YACA;QACF;QAEA,qBAAqB;QACrB,MAAM,gBAAgB,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aAAa,QAAQ;QACvD,MAAM,aAAa,cAAc,KAAK,IAAI;QAC1C,MAAM,cAAc,cAAc,MAAM,IAAI;QAE5C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,aAAa;QAE5D,4BAA4B;QAC5B,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,CAAC,kBAAkB,GAAG;QAC/D,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,CAAC,iBAAiB,GAAG;QAE7D,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,aAAa,EAAE,QAAQ,EAAE,CAAC;QAE7D,qDAAqD;QACrD,MAAM,gBAAgB,MAAM,SAAS,UAAU,UAAU;QAEzD,sDAAsD;QACtD,MAAM,WAAW,aAAa,WAAW;QACzC,MAAM,UAAU,cAAc,WAAW;QAEzC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,SAAS;QAE7D,mCAAmC;QACnC,MAAM,EAAE,MAAM,QAAQ,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,eACpD,WAAW,GACX,GAAG,GACH,QAAQ,CAAC;YAAE,mBAAmB;QAAK;QAEtC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;YAC3C,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG;QACzC;QAEA,MAAM,cAAc,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,UAAU;YAAE,KAAK;QAAS,GAAG,GAAG,GAAG,QAAQ;QAE3E,oCAAoC;QACpC,MAAM,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aAC9B,SAAS,CAAC;YAAC;gBACV,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,OAAO;YACT;SAAE,EACD,GAAG,GACH,QAAQ;QAEX,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,aAAa,MAAM,CAAC,MAAM,CAAC;QAC5F,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC3G;AACF;AAKO,eAAe,qBACpB,WAAmB,EACnB,QAAgB,EAChB,OAIC;IAED,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,0DAA0D;QAC1D,MAAM,aAAa,YAAY,OAAO,CAAC,+BAA+B;QACtE,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;QAE5C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY,MAAM,CAAC,MAAM,CAAC;QAElE,mBAAmB;QACnB,MAAM,kBAAkB,MAAM,eAAe,aAAa,UAAU;QAEpE,kCAAkC;QAClC,MAAM,eAAe,CAAC,sBAAsB,EAAE,gBAAgB,QAAQ,CAAC,WAAW;QAElF,QAAQ,GAAG,CAAC;QACZ,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAClH;AACF;AAKO,eAAe,cACpB,MAAc,EACd,UAKI,CAAC,CAAC;IAEN,MAAM,EACJ,WAAW,IAAI,EACf,YAAY,IAAI,EAChB,UAAU,EAAE,EACZ,SAAS,MAAM,EAChB,GAAG;IAEJ,QAAQ,GAAG,CAAC,CAAC,8BAA8B,CAAC,EAAE;QAAE;QAAU;QAAW;QAAS;IAAO;IAErF,IAAI,WAAW,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QAClB,MAAM,CAAC,UAAU,WAAW;QAC3B,KAAK;QACL,oBAAoB;IACtB;IAEF,OAAQ;QACN,KAAK;YACH,WAAW,SAAS,IAAI,CAAC;gBAAE;YAAQ;YACnC;QACF,KAAK;YACH,WAAW,SAAS,GAAG,CAAC;gBAAE,kBAAkB;YAAE;YAC9C;QACF,KAAK;YACH,WAAW,SAAS,IAAI,CAAC;gBAAE;YAAQ;YACnC;IACJ;IAEA,MAAM,SAAS,MAAM,SAAS,QAAQ;IACtC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC;IAE/E,OAAO;AACT", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/api/process/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@/lib/supabase/server';\nimport OpenAI from 'openai';\nimport sharp from 'sharp';\nimport { enforceRateLimit, decrementLimits, logGenerationAttempt } from '@/lib/rateLimiter';\nimport { processImageWithLogo } from '@/lib/imageUtils';\nimport path from 'path';\n\n// OpenAI client will be instantiated in the handler to avoid build-time issues\n\nexport async function POST(request: NextRequest) {\n  let userId: number = 0;\n  let rateLimitStatus: { \n    userRemaining: number;\n    globalRemaining: number;\n    userLimit: number;\n    globalLimit: number;\n  } | null = null;\n\n  try {\n    // Initialize OpenAI client\n    const openai = new OpenAI({\n      apiKey: process.env.OPENAI_API_KEY,\n    });\n\n    // Check authentication\n    const supabase = await createClient();\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n\n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    console.log('🔐 Authenticated user:', user.id);\n\n    // Check rate limits before processing\n    console.log('🛡️ Checking rate limits...');\n    const rateLimitResult = await enforceRateLimit(user.id);\n    userId = rateLimitResult.userId;\n    rateLimitStatus = rateLimitResult.status;\n\n    if (!rateLimitResult.allowed) {\n      console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);\n\n      // Log the blocked attempt\n      await logGenerationAttempt({\n        userId,\n        prompt: 'Rate limit exceeded',\n        success: false,\n        errorMessage: rateLimitResult.status.reason\n      });\n\n      return NextResponse.json({\n        error: rateLimitResult.status.reason,\n        rateLimitStatus: rateLimitResult.status\n      }, { status: 429 });\n    }\n\n    console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);\n\n    const { imageData, prompt } = await request.json();\n\n    if (!imageData || !prompt) {\n      return NextResponse.json({ \n        error: 'Image data and prompt are required' \n      }, { status: 400 });\n    }\n\n    // Validate prompt length for gpt-image-1\n    if (prompt.length > 32000) {\n      return NextResponse.json({ \n        error: 'Prompt too long. Maximum 32000 characters for gpt-image-1.' \n      }, { status: 400 });\n    }\n\n    // Convert base64 image to buffer and ensure it's in the right format\n    const base64Data = imageData.replace(/^data:image\\/[a-z]+;base64,/, '');\n    const imageBuffer = Buffer.from(base64Data, 'base64');\n\n    // Convert to PNG format using Sharp for better compatibility\n    const pngBuffer = await sharp(imageBuffer)\n      .png()\n      .resize(1024, 1024, { \n        fit: 'inside', \n        withoutEnlargement: true,\n        background: { r: 255, g: 255, b: 255, alpha: 1 }\n      })\n      .toBuffer();\n\n    // Create a proper File stream for OpenAI\n    const imageBlob = new Blob([pngBuffer], { type: 'image/png' });\n    const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });\n\n    console.log('Sending image to OpenAI:', {\n      originalSize: imageBuffer.length,\n      processedSize: pngBuffer.length,\n      prompt: prompt,\n      fileType: imageFile.type\n    });\n\n    // Use gpt-image-1 for image editing\n    console.log('Using gpt-image-1 for image transformation...');\n    const response = await openai.images.edit({\n      model: \"gpt-image-1\",\n      image: imageFile,\n      prompt: prompt,\n      n: 1,\n      size: \"1024x1024\",\n      output_format: \"png\",\n      input_fidelity: \"high\",\n      quality: \"medium\"\n    });\n    \n    const modelUsed = \"gpt-image-1\";\n\n    console.log('Model used:', modelUsed);\n    console.log('Response data length:', response.data?.length);\n\n    let editedImageUrl = null;\n    \n    if (response.data && Array.isArray(response.data) && response.data.length > 0) {\n      const firstResult = response.data[0];\n      \n      // gpt-image-1 returns base64-encoded images\n      if (firstResult.b64_json) {\n        // Convert base64 to data URL\n        editedImageUrl = `data:image/png;base64,${firstResult.b64_json}`;\n        console.log('Generated data URL from base64 for gpt-image-1');\n      }\n    }\n\n    if (!editedImageUrl) {\n      console.error('No image data found in OpenAI response. Full response:', JSON.stringify(response, null, 2));\n\n      // Log the failed generation attempt\n      await logGenerationAttempt({\n        userId,\n        prompt,\n        success: false,\n        errorMessage: `No edited image data found. Model: ${modelUsed}`\n      });\n\n      throw new Error(`No edited image data found. Model: ${modelUsed}, Response keys: ${response.data?.[0] ? Object.keys(response.data[0]).join(', ') : 'no data'}`);\n    }\n\n    // Generation was successful - decrement the rate limits\n    console.log('✅ Generation successful, decrementing rate limits...');\n    const decrementSuccess = await decrementLimits(userId);\n\n    if (!decrementSuccess) {\n      console.error('⚠️ Failed to decrement rate limits after successful generation');\n      // Continue anyway since the generation was successful\n    }\n\n    // Log the successful generation attempt\n    await logGenerationAttempt({\n      userId,\n      prompt,\n      success: true,\n      imageUrl: editedImageUrl\n    });\n\n    console.log('🎉 Generation completed successfully');\n\n    // Add logo overlay to the processed image\n    console.log('Adding logo overlay to processed image...');\n    const logoPath = path.join(process.cwd(), 'public', 'logo.svg');\n\n    try {\n      const imageWithLogo = await processImageWithLogo(editedImageUrl, logoPath, {\n        logoSizePercent: 12,  // 12% of image width\n        paddingPercent: 3,    // 3% padding from edges\n        opacity: 0.9          // 90% opacity for clear visibility\n      });\n\n      console.log('Logo overlay added successfully');\n      editedImageUrl = imageWithLogo;\n\n    } catch (logoError) {\n      console.error('Failed to add logo overlay:', logoError);\n      // Continue without logo if overlay fails - don't break the main functionality\n      console.log('Continuing without logo overlay due to error');\n    }\n\n    return NextResponse.json({\n      success: true,\n      editedImageUrl,\n      originalPrompt: prompt,\n      processedAt: new Date().toISOString(),\n      model: modelUsed,\n      rateLimitStatus: {\n        userRemaining: Math.max(0, rateLimitStatus.userRemaining - 1),\n        globalRemaining: Math.max(0, rateLimitStatus.globalRemaining - 1),\n        userLimit: rateLimitStatus.userLimit,\n        globalLimit: rateLimitStatus.globalLimit\n      }\n    });\n\n  } catch (error) {\n    console.error('Processing error:', error);\n\n    // Log the failed generation attempt if we have user info\n    if (userId > 0) {\n      await logGenerationAttempt({\n        userId,\n        prompt: 'Error occurred during processing',\n        success: false,\n        errorMessage: error instanceof Error ? error.message : 'Unknown error'\n      });\n    }\n\n    // Handle specific OpenAI errors\n    if (error instanceof OpenAI.APIError) {\n      if (error.status === 401) {\n        return NextResponse.json({\n          error: 'Invalid API key configuration'\n        }, { status: 500 });\n      }\n      if (error.status === 429) {\n        return NextResponse.json({\n          error: 'OpenAI API rate limit exceeded. Please try again later.'\n        }, { status: 429 });\n      }\n      if (error.status === 400) {\n        return NextResponse.json({\n          error: 'Invalid image or prompt. Please check your input and try again.'\n        }, { status: 400 });\n      }\n      return NextResponse.json({\n        error: `API error: ${error.message}`\n      }, { status: error.status || 500 });\n    }\n\n    return NextResponse.json({\n      error: 'Processing failed. Please try again.'\n    }, { status: 500 });\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAIO,eAAe,KAAK,OAAoB;IAC7C,IAAI,SAAiB;IACrB,IAAI,kBAKO;IAEX,IAAI;QACF,2BAA2B;QAC3B,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;YACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;QACpC;QAEA,uBAAuB;QACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,EAAE;QAE7C,sCAAsC;QACtC,QAAQ,GAAG,CAAC;QACZ,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE;QACtD,SAAS,gBAAgB,MAAM;QAC/B,kBAAkB,gBAAgB,MAAM;QAExC,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,QAAQ,GAAG,CAAC,0BAA0B,gBAAgB,MAAM,CAAC,MAAM;YAEnE,0BAA0B;YAC1B,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB;gBACA,QAAQ;gBACR,SAAS;gBACT,cAAc,gBAAgB,MAAM,CAAC,MAAM;YAC7C;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,gBAAgB,MAAM,CAAC,MAAM;gBACpC,iBAAiB,gBAAgB,MAAM;YACzC,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,gDAAgD,gBAAgB,aAAa,GAAG,cAAc,gBAAgB,eAAe;QAEzI,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhD,IAAI,CAAC,aAAa,CAAC,QAAQ;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yCAAyC;QACzC,IAAI,OAAO,MAAM,GAAG,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qEAAqE;QACrE,MAAM,aAAa,UAAU,OAAO,CAAC,+BAA+B;QACpE,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;QAE5C,6DAA6D;QAC7D,MAAM,YAAY,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aAC3B,GAAG,GACH,MAAM,CAAC,MAAM,MAAM;YAClB,KAAK;YACL,oBAAoB;YACpB,YAAY;gBAAE,GAAG;gBAAK,GAAG;gBAAK,GAAG;gBAAK,OAAO;YAAE;QACjD,GACC,QAAQ;QAEX,yCAAyC;QACzC,MAAM,YAAY,IAAI,KAAK;YAAC;SAAU,EAAE;YAAE,MAAM;QAAY;QAC5D,MAAM,YAAY,IAAI,KAAK;YAAC;SAAU,EAAE,aAAa;YAAE,MAAM;QAAY;QAEzE,QAAQ,GAAG,CAAC,4BAA4B;YACtC,cAAc,YAAY,MAAM;YAChC,eAAe,UAAU,MAAM;YAC/B,QAAQ;YACR,UAAU,UAAU,IAAI;QAC1B;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACxC,OAAO;YACP,OAAO;YACP,QAAQ;YACR,GAAG;YACH,MAAM;YACN,eAAe;YACf,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,YAAY;QAElB,QAAQ,GAAG,CAAC,eAAe;QAC3B,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI,EAAE;QAEpD,IAAI,iBAAiB;QAErB,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;YAC7E,MAAM,cAAc,SAAS,IAAI,CAAC,EAAE;YAEpC,4CAA4C;YAC5C,IAAI,YAAY,QAAQ,EAAE;gBACxB,6BAA6B;gBAC7B,iBAAiB,CAAC,sBAAsB,EAAE,YAAY,QAAQ,EAAE;gBAChE,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB,QAAQ,KAAK,CAAC,0DAA0D,KAAK,SAAS,CAAC,UAAU,MAAM;YAEvG,oCAAoC;YACpC,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB;gBACA;gBACA,SAAS;gBACT,cAAc,CAAC,mCAAmC,EAAE,WAAW;YACjE;YAEA,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,UAAU,iBAAiB,EAAE,SAAS,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,WAAW;QAChK;QAEA,wDAAwD;QACxD,QAAQ,GAAG,CAAC;QACZ,MAAM,mBAAmB,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QAE/C,IAAI,CAAC,kBAAkB;YACrB,QAAQ,KAAK,CAAC;QACd,sDAAsD;QACxD;QAEA,wCAAwC;QACxC,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE;YACzB;YACA;YACA,SAAS;YACT,UAAU;QACZ;QAEA,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QAEpD,IAAI;YACF,MAAM,gBAAgB,MAAM,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,UAAU;gBACzE,iBAAiB;gBACjB,gBAAgB;gBAChB,SAAS,IAAa,mCAAmC;YAC3D;YAEA,QAAQ,GAAG,CAAC;YACZ,iBAAiB;QAEnB,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,8EAA8E;YAC9E,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,gBAAgB;YAChB,aAAa,IAAI,OAAO,WAAW;YACnC,OAAO;YACP,iBAAiB;gBACf,eAAe,KAAK,GAAG,CAAC,GAAG,gBAAgB,aAAa,GAAG;gBAC3D,iBAAiB,KAAK,GAAG,CAAC,GAAG,gBAAgB,eAAe,GAAG;gBAC/D,WAAW,gBAAgB,SAAS;gBACpC,aAAa,gBAAgB,WAAW;YAC1C;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,yDAAyD;QACzD,IAAI,SAAS,GAAG;YACd,MAAM,CAAA,GAAA,oHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB;gBACA,QAAQ;gBACR,SAAS;gBACT,cAAc,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD;QACF;QAEA,gCAAgC;QAChC,IAAI,iBAAiB,wKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;YACpC,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,IAAI,MAAM,MAAM,KAAK,KAAK;gBACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;YACtC,GAAG;gBAAE,QAAQ,MAAM,MAAM,IAAI;YAAI;QACnC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}