{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\nimport { cookies } from \"next/headers\";\n\n/**\n * Especially important if using Fluid compute: Don't put this client in a\n * global variable. Always create a new client within each function when using\n * it.\n */\nexport async function createClient() {\n  const cookieStore = await cookies();\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll();\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options),\n            );\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    },\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAOO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@/lib/supabase/server';\nimport sharp from 'sharp';\n\nconst MAX_FILE_SIZE = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '10485760'); // 10MB\nconst ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check authentication\n    const supabase = await createClient();\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    }\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });\n    }\n\n    // Validate file type\n    if (!ALLOWED_TYPES.includes(file.type)) {\n      return NextResponse.json({ \n        error: 'Invalid file type. Please upload a JPEG, PNG, or WebP image.' \n      }, { status: 400 });\n    }\n\n    // Validate file size\n    if (file.size > MAX_FILE_SIZE) {\n      return NextResponse.json({ \n        error: `File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB.` \n      }, { status: 400 });\n    }\n\n    // Convert file to buffer\n    const buffer = Buffer.from(await file.arrayBuffer());\n\n    // Optimize image with sharp\n    const optimizedBuffer = await sharp(buffer)\n      .resize(2048, 2048, { \n        fit: 'inside', \n        withoutEnlargement: true \n      })\n      .jpeg({ quality: 85 })\n      .toBuffer();\n\n    // Convert to base64 for API processing\n    const base64Image = `data:image/jpeg;base64,${optimizedBuffer.toString('base64')}`;\n\n    // Get image metadata\n    const metadata = await sharp(buffer).metadata();\n\n    return NextResponse.json({\n      success: true,\n      image: base64Image,\n      metadata: {\n        width: metadata.width,\n        height: metadata.height,\n        format: metadata.format,\n        size: optimizedBuffer.length,\n        originalSize: file.size,\n        filename: file.name\n      }\n    });\n\n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json({ \n      error: 'Upload failed. Please try again.' \n    }, { status: 500 });\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB,SAAS,gDAAyC,aAAa,OAAO;AAC5F,MAAM,gBAAgB;IAAC;IAAc;IAAa;CAAa;AAExD,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,uBAAuB;QACvB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,qBAAqB;QACrB,IAAI,CAAC,cAAc,QAAQ,CAAC,KAAK,IAAI,GAAG;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qBAAqB;QACrB,IAAI,KAAK,IAAI,GAAG,eAAe;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,gCAAgC,EAAE,gBAAgB,OAAO,KAAK,GAAG,CAAC;YAC5E,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,yBAAyB;QACzB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QAEjD,4BAA4B;QAC5B,MAAM,kBAAkB,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACjC,MAAM,CAAC,MAAM,MAAM;YAClB,KAAK;YACL,oBAAoB;QACtB,GACC,IAAI,CAAC;YAAE,SAAS;QAAG,GACnB,QAAQ;QAEX,uCAAuC;QACvC,MAAM,cAAc,CAAC,uBAAuB,EAAE,gBAAgB,QAAQ,CAAC,WAAW;QAElF,qBAAqB;QACrB,MAAM,WAAW,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QAAQ,QAAQ;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;YACP,UAAU;gBACR,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;gBACvB,QAAQ,SAAS,MAAM;gBACvB,MAAM,gBAAgB,MAAM;gBAC5B,cAAc,KAAK,IAAI;gBACvB,UAAU,KAAK,IAAI;YACrB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}