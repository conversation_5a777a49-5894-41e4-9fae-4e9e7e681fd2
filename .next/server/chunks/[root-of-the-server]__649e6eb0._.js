module.exports = {

"[project]/.next-internal/server/app/api/process/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/lib/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createClient": ()=>createClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://jtqmhihkqrnhorrgwbqp.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}),
"[externals]/sharp [external] (sharp, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("sharp", () => require("sharp"));

module.exports = mod;
}}),
"[project]/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "cn": ()=>cn,
    "hasEnvVars": ()=>hasEnvVars,
    "hasRateLimitingEnvVars": ()=>hasRateLimitingEnvVars,
    "validateRateLimitingEnvironment": ()=>validateRateLimitingEnvironment
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const hasEnvVars = ("TURBOPACK compile-time value", "https://jtqmhihkqrnhorrgwbqp.supabase.co") && ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg");
const hasRateLimitingEnvVars = hasEnvVars && process.env.SUPABASE_SERVICE_KEY;
function validateRateLimitingEnvironment() {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    if (!process.env.SUPABASE_SERVICE_KEY) {
        throw new Error('Missing SUPABASE_SERVICE_KEY environment variable - required for rate limiting');
    }
}
}),
"[project]/lib/rateLimiter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "checkGenerationAllowed": ()=>checkGenerationAllowed,
    "decrementLimits": ()=>decrementLimits,
    "enforceRateLimit": ()=>enforceRateLimit,
    "getRateLimitStatus": ()=>getRateLimitStatus,
    "getUserNumericId": ()=>getUserNumericId,
    "logGenerationAttempt": ()=>logGenerationAttempt
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-route] (ecmascript)");
;
;
// Create service-level Supabase client for rate limiting operations
function createServiceClient() {
    // Validate all required environment variables are present
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateRateLimitingEnvironment"])();
    const supabaseUrl = ("TURBOPACK compile-time value", "https://jtqmhihkqrnhorrgwbqp.supabase.co");
    const serviceKey = process.env.SUPABASE_SERVICE_KEY;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, serviceKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
}
async function getUserNumericId(supabaseUuid) {
    console.log('🔄 Mapping Supabase UUID to numeric ID:', supabaseUuid);
    try {
        const supabase = createServiceClient();
        const { data, error } = await supabase.rpc('get_or_create_numeric_user_id', {
            supabase_uuid: supabaseUuid
        });
        if (error) {
            console.error('❌ Error mapping user ID:', error);
            throw new Error(`Failed to map user ID: ${error.message}`);
        }
        console.log('✅ Mapped to numeric ID:', data);
        return data;
    } catch (error) {
        console.error('❌ Critical error in getUserNumericId:', error);
        throw error;
    }
}
async function checkGenerationAllowed(userId) {
    console.log('🔍 Checking generation limits for user:', userId);
    try {
        const supabase = createServiceClient();
        const { data, error } = await supabase.rpc('check_generation_allowed', {
            user_id_param: userId
        });
        if (error) {
            console.error('❌ Error checking generation limits:', error);
            throw new Error(`Failed to check limits: ${error.message}`);
        }
        console.log('📊 Rate limit status:', data);
        const status = {
            allowed: data.allowed,
            userRemaining: data.user_remaining,
            userLimit: data.user_limit,
            globalRemaining: data.global_remaining,
            globalLimit: data.global_limit,
            resetTime: 'midnight UTC',
            reason: data.allowed ? undefined : data.user_remaining <= 0 ? 'User daily limit exceeded' : 'Global daily limit exceeded'
        };
        return status;
    } catch (error) {
        console.error('❌ Critical error in checkGenerationAllowed:', error);
        throw error;
    }
}
async function decrementLimits(userId) {
    console.log('⬇️ Decrementing limits for user:', userId);
    try {
        const supabase = createServiceClient();
        const { data, error } = await supabase.rpc('decrement_both_limits', {
            user_id_param: userId
        });
        if (error) {
            console.error('❌ Error decrementing limits:', error);
            throw new Error(`Failed to decrement limits: ${error.message}`);
        }
        console.log('✅ Limits decremented successfully:', data);
        return data;
    } catch (error) {
        console.error('❌ Critical error in decrementLimits:', error);
        throw error;
    }
}
async function logGenerationAttempt(entry) {
    console.log('📝 Logging generation attempt for user:', entry.userId);
    try {
        const supabase = createServiceClient();
        const { error } = await supabase.from('cry_generation_logs').insert({
            user_id: entry.userId,
            prompt: entry.prompt.substring(0, 1000),
            success: entry.success,
            image_url: entry.imageUrl,
            error_message: entry.errorMessage
        });
        if (error) {
            console.error('❌ Error logging generation attempt:', error);
        // Don't throw here - logging failures shouldn't break the main flow
        } else {
            console.log('✅ Generation attempt logged successfully');
        }
    } catch (error) {
        console.error('❌ Critical error in logGenerationAttempt:', error);
    // Don't throw here - logging failures shouldn't break the main flow
    }
}
async function getRateLimitStatus(userId) {
    return checkGenerationAllowed(userId);
}
async function enforceRateLimit(supabaseUuid) {
    console.log('🛡️ Enforcing rate limit for UUID:', supabaseUuid);
    try {
        // Step 1: Map UUID to numeric ID
        const userId = await getUserNumericId(supabaseUuid);
        // Step 2: Check if generation is allowed
        const status = await checkGenerationAllowed(userId);
        console.log('🛡️ Rate limit enforcement result:', {
            allowed: status.allowed,
            reason: status.reason,
            userRemaining: status.userRemaining,
            globalRemaining: status.globalRemaining
        });
        return {
            allowed: status.allowed,
            status,
            userId
        };
    } catch (error) {
        console.error('❌ Critical error in enforceRateLimit:', error);
        // In case of database errors, we fail closed (deny the request)
        // This is safer than allowing unlimited requests
        return {
            allowed: false,
            status: {
                allowed: false,
                userRemaining: 0,
                userLimit: 0,
                globalRemaining: 0,
                globalLimit: 0,
                resetTime: 'midnight UTC',
                reason: 'Rate limiting system temporarily unavailable'
            },
            userId: 0
        };
    }
}
}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/lib/imageUtils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "addLogoOverlay": ()=>addLogoOverlay,
    "optimizeImage": ()=>optimizeImage,
    "processImageWithLogo": ()=>processImageWithLogo,
    "svgToPng": ()=>svgToPng
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sharp [external] (sharp, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
;
;
async function svgToPng(svgPath, width, height) {
    try {
        console.log(`Converting SVG to PNG: ${svgPath}, dimensions: ${width}x${height}`);
        // Read SVG file
        const svgBuffer = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].promises.readFile(svgPath);
        // Convert SVG to PNG using Sharp
        const pngBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(svgBuffer).resize(width, height, {
            fit: 'contain',
            background: {
                r: 0,
                g: 0,
                b: 0,
                alpha: 0
            } // Transparent background
        }).png().toBuffer();
        console.log(`SVG converted to PNG successfully. Output size: ${pngBuffer.length} bytes`);
        return pngBuffer;
    } catch (error) {
        console.error('Error converting SVG to PNG:', error);
        throw new Error(`Failed to convert SVG to PNG: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function addLogoOverlay(imageBuffer, logoPath, options = {}) {
    try {
        const { logoSizePercent = 12, paddingPercent = 3, opacity = 0.9 // 90% opacity
         } = options;
        console.log('Starting logo overlay process with options:', {
            logoSizePercent,
            paddingPercent,
            opacity,
            logoPath
        });
        // Get image metadata
        const imageMetadata = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(imageBuffer).metadata();
        const imageWidth = imageMetadata.width || 1024;
        const imageHeight = imageMetadata.height || 1024;
        console.log(`Image dimensions: ${imageWidth}x${imageHeight}`);
        // Calculate logo dimensions
        const logoSize = Math.round(imageWidth * (logoSizePercent / 100));
        const padding = Math.round(imageWidth * (paddingPercent / 100));
        console.log(`Logo size: ${logoSize}px, Padding: ${padding}px`);
        // Convert SVG logo to PNG with calculated dimensions
        const logoPngBuffer = await svgToPng(logoPath, logoSize, logoSize);
        // Calculate logo position (bottom right with padding)
        const logoLeft = imageWidth - logoSize - padding;
        const logoTop = imageHeight - logoSize - padding;
        console.log(`Logo position: left=${logoLeft}, top=${logoTop}`);
        // Create logo overlay with opacity
        const { data: logoData, info: logoInfo } = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(logoPngBuffer).ensureAlpha().raw().toBuffer({
            resolveWithObject: true
        });
        for(let i = 3; i < logoData.length; i += 4){
            logoData[i] = Math.round(logoData[i] * opacity);
        }
        const logoOverlay = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(logoData, {
            raw: logoInfo
        }).png().toBuffer();
        // Composite the logo onto the image
        const resultBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(imageBuffer).composite([
            {
                input: logoOverlay,
                left: logoLeft,
                top: logoTop,
                blend: 'over'
            }
        ]).png().toBuffer();
        console.log(`Logo overlay completed successfully. Result size: ${resultBuffer.length} bytes`);
        return resultBuffer;
    } catch (error) {
        console.error('Error adding logo overlay:', error);
        throw new Error(`Failed to add logo overlay: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function processImageWithLogo(base64Image, logoPath, options) {
    try {
        console.log('Processing base64 image with logo overlay');
        // Extract base64 data (remove data URL prefix if present)
        const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
        const imageBuffer = Buffer.from(base64Data, 'base64');
        console.log(`Input image buffer size: ${imageBuffer.length} bytes`);
        // Add logo overlay
        const processedBuffer = await addLogoOverlay(imageBuffer, logoPath, options);
        // Convert back to base64 data URL
        const resultBase64 = `data:image/png;base64,${processedBuffer.toString('base64')}`;
        console.log('Image processing with logo completed successfully');
        return resultBase64;
    } catch (error) {
        console.error('Error processing image with logo:', error);
        throw new Error(`Failed to process image with logo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function optimizeImage(buffer, options = {}) {
    const { maxWidth = 1024, maxHeight = 1024, quality = 85, format = 'jpeg' } = options;
    console.log(`Optimizing image with options:`, {
        maxWidth,
        maxHeight,
        quality,
        format
    });
    let pipeline = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer).resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
    });
    switch(format){
        case 'jpeg':
            pipeline = pipeline.jpeg({
                quality
            });
            break;
        case 'png':
            pipeline = pipeline.png({
                compressionLevel: 8
            });
            break;
        case 'webp':
            pipeline = pipeline.webp({
                quality
            });
            break;
    }
    const result = await pipeline.toBuffer();
    console.log(`Image optimization completed. Output size: ${result.length} bytes`);
    return result;
}
}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/app/api/process/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sharp [external] (sharp, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/rateLimiter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$imageUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/imageUtils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
;
async function POST(request) {
    let userId = 0;
    let rateLimitStatus = null;
    try {
        // Initialize OpenAI client
        const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey: process.env.OPENAI_API_KEY
        });
        // Check authentication
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        console.log('🔐 Authenticated user:', user.id);
        // Check rate limits before processing
        console.log('🛡️ Checking rate limits...');
        const rateLimitResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enforceRateLimit"])(user.id);
        userId = rateLimitResult.userId;
        rateLimitStatus = rateLimitResult.status;
        if (!rateLimitResult.allowed) {
            console.log('❌ Rate limit exceeded:', rateLimitResult.status.reason);
            // Log the blocked attempt
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logGenerationAttempt"])({
                userId,
                prompt: 'Rate limit exceeded',
                success: false,
                errorMessage: rateLimitResult.status.reason
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: rateLimitResult.status.reason,
                rateLimitStatus: rateLimitResult.status
            }, {
                status: 429
            });
        }
        console.log('✅ Rate limit check passed. Remaining: User=' + rateLimitStatus.userRemaining + ', Global=' + rateLimitStatus.globalRemaining);
        const { imageData, prompt } = await request.json();
        if (!imageData || !prompt) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Image data and prompt are required'
            }, {
                status: 400
            });
        }
        // Validate prompt length for gpt-image-1
        if (prompt.length > 32000) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Prompt too long. Maximum 32000 characters for gpt-image-1.'
            }, {
                status: 400
            });
        }
        // Convert base64 image to buffer and ensure it's in the right format
        const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
        const imageBuffer = Buffer.from(base64Data, 'base64');
        // Convert to PNG format using Sharp for better compatibility
        const pngBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(imageBuffer).png().resize(1024, 1024, {
            fit: 'inside',
            withoutEnlargement: true,
            background: {
                r: 255,
                g: 255,
                b: 255,
                alpha: 1
            }
        }).toBuffer();
        // Create a proper File stream for OpenAI
        const imageBlob = new Blob([
            pngBuffer
        ], {
            type: 'image/png'
        });
        const imageFile = new File([
            imageBlob
        ], 'image.png', {
            type: 'image/png'
        });
        console.log('Sending image to OpenAI:', {
            originalSize: imageBuffer.length,
            processedSize: pngBuffer.length,
            prompt: prompt,
            fileType: imageFile.type
        });
        // Use gpt-image-1 for image editing
        console.log('Using gpt-image-1 for image transformation...');
        const response = await openai.images.edit({
            model: "gpt-image-1",
            image: imageFile,
            prompt: prompt,
            n: 1,
            size: "1024x1024",
            output_format: "png",
            input_fidelity: "high",
            quality: "medium"
        });
        const modelUsed = "gpt-image-1";
        console.log('Model used:', modelUsed);
        console.log('Response data length:', response.data?.length);
        let editedImageUrl = null;
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
            const firstResult = response.data[0];
            // gpt-image-1 returns base64-encoded images
            if (firstResult.b64_json) {
                // Convert base64 to data URL
                editedImageUrl = `data:image/png;base64,${firstResult.b64_json}`;
                console.log('Generated data URL from base64 for gpt-image-1');
            }
        }
        if (!editedImageUrl) {
            console.error('No image data found in OpenAI response. Full response:', JSON.stringify(response, null, 2));
            // Log the failed generation attempt
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logGenerationAttempt"])({
                userId,
                prompt,
                success: false,
                errorMessage: `No edited image data found. Model: ${modelUsed}`
            });
            throw new Error(`No edited image data found. Model: ${modelUsed}, Response keys: ${response.data?.[0] ? Object.keys(response.data[0]).join(', ') : 'no data'}`);
        }
        // Generation was successful - decrement the rate limits
        console.log('✅ Generation successful, decrementing rate limits...');
        const decrementSuccess = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["decrementLimits"])(userId);
        if (!decrementSuccess) {
            console.error('⚠️ Failed to decrement rate limits after successful generation');
        // Continue anyway since the generation was successful
        }
        // Log the successful generation attempt
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logGenerationAttempt"])({
            userId,
            prompt,
            success: true,
            imageUrl: editedImageUrl
        });
        console.log('🎉 Generation completed successfully');
        // Add logo overlay to the processed image
        console.log('Adding logo overlay to processed image...');
        const logoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'logo.svg');
        try {
            const imageWithLogo = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$imageUtils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processImageWithLogo"])(editedImageUrl, logoPath, {
                logoSizePercent: 12,
                paddingPercent: 3,
                opacity: 0.9 // 90% opacity for clear visibility
            });
            console.log('Logo overlay added successfully');
            editedImageUrl = imageWithLogo;
        } catch (logoError) {
            console.error('Failed to add logo overlay:', logoError);
            // Continue without logo if overlay fails - don't break the main functionality
            console.log('Continuing without logo overlay due to error');
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            editedImageUrl,
            originalPrompt: prompt,
            processedAt: new Date().toISOString(),
            model: modelUsed,
            rateLimitStatus: {
                userRemaining: Math.max(0, rateLimitStatus.userRemaining - 1),
                globalRemaining: Math.max(0, rateLimitStatus.globalRemaining - 1),
                userLimit: rateLimitStatus.userLimit,
                globalLimit: rateLimitStatus.globalLimit
            }
        });
    } catch (error) {
        console.error('Processing error:', error);
        // Log the failed generation attempt if we have user info
        if (userId > 0) {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logGenerationAttempt"])({
                userId,
                prompt: 'Error occurred during processing',
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
            });
        }
        // Handle specific OpenAI errors
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"].APIError) {
            if (error.status === 401) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid API key configuration'
                }, {
                    status: 500
                });
            }
            if (error.status === 429) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'OpenAI API rate limit exceeded. Please try again later.'
                }, {
                    status: 429
                });
            }
            if (error.status === 400) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid image or prompt. Please check your input and try again.'
                }, {
                    status: 400
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `API error: ${error.message}`
            }, {
                status: error.status || 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Processing failed. Please try again.'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__649e6eb0._.js.map