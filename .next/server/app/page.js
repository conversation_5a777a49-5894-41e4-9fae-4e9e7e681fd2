(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1322:(a,b)=>{"use strict";function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4860:(a,b,c)=>{Promise.resolve().then(c.bind(c,54656)),Promise.resolve().then(c.t.bind(c,49603,23))},9131:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(21122);let d=c(1322),e=c(27894),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11469:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},11997:a=>{"use strict";a.exports=require("punycode")},14163:(a,b,c)=>{"use strict";c.d(b,{hO:()=>i,sG:()=>h});var d=c(43210),e=c(51215),f=c(8730),g=c(60687),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,f.TL)(`Primitive.${b}`),e=d.forwardRef((a,d)=>{let{asChild:e,...f}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,g.jsx)(e?c:b,{...f,ref:d})});return e.displayName=`Primitive.${b}`,{...a,[b]:e}},{});function i(a,b){a&&e.flushSync(()=>a.dispatchEvent(b))}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"warnOnce",{enumerable:!0,get:function(){return c}});let c=a=>{}},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27894:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:a=>{"use strict";a.exports=require("stream")},28086:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(a,b)=>{"use strict";function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},33618:(a,b,c)=>{Promise.resolve().then(c.bind(c,10218))},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35949:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,91449)),"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39727:()=>{},40478:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d0});var d,e,f,g=c(60687),h=c(24934),i=c(43210),j=c.t(i,2);function k(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var l=c(98599);function m(a,b=[]){let c=[],d=()=>{let b=c.map(a=>i.createContext(a));return function(c){let d=c?.[a]||b;return i.useMemo(()=>({[`__scope${a}`]:{...c,[a]:d}}),[c,d])}};return d.scopeName=a,[function(b,d){let e=i.createContext(d),f=c.length;c=[...c,d];let h=b=>{let{scope:c,children:d,...h}=b,j=c?.[a]?.[f]||e,k=i.useMemo(()=>h,Object.values(h));return(0,g.jsx)(j.Provider,{value:k,children:d})};return h.displayName=b+"Provider",[h,function(c,g){let h=g?.[a]?.[f]||e,j=i.useContext(h);if(j)return j;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let d=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return i.useMemo(()=>({[`__scope${b.scopeName}`]:d}),[d])}};return c.scopeName=b.scopeName,c}(d,...b)]}var n=globalThis?.document?i.useLayoutEffect:()=>{},o=j[" useInsertionEffect ".trim().toString()]||n;function p({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,g]=function({defaultProp:a,onChange:b}){let[c,d]=i.useState(a),e=i.useRef(c),f=i.useRef(b);return o(()=>{f.current=b},[b]),i.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),h=void 0!==a,j=h?a:e;{let b=i.useRef(void 0!==a);i.useEffect(()=>{let a=b.current;if(a!==h){let b=h?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=h},[h,d])}return[j,i.useCallback(b=>{if(h){let c="function"==typeof b?b(a):b;c!==a&&g.current?.(c)}else f(b)},[h,a,f,g])]}Symbol("RADIX:SYNC_STATE");var q=c(14163),r=c(8730);function s(a){let b=a+"CollectionProvider",[c,d]=m(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),h=a=>{let{scope:b,children:c}=a,d=i.useRef(null),f=i.useRef(new Map).current;return(0,g.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};h.displayName=b;let j=a+"CollectionSlot",k=(0,r.TL)(j),n=i.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=f(j,c),h=(0,l.s)(b,e.collectionRef);return(0,g.jsx)(k,{ref:h,children:d})});n.displayName=j;let o=a+"CollectionItemSlot",p="data-radix-collection-item",q=(0,r.TL)(o),s=i.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,h=i.useRef(null),j=(0,l.s)(b,h),k=f(o,c);return i.useEffect(()=>(k.itemMap.set(h,{ref:h,...e}),()=>void k.itemMap.delete(h))),(0,g.jsx)(q,{...{[p]:""},ref:j,children:d})});return s.displayName=o,[{Provider:h,Slot:n,ItemSlot:s},function(b){let c=f(a+"CollectionConsumer",b);return i.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${p}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}var t=new WeakMap;function u(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=v(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function v(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],t.set(this,!0)}set(a,b){return t.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=v(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=u(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=u(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return u(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var w=i.createContext(void 0);function x(a){let b=i.useContext(w);return a||b||"ltr"}function y(a){let b=i.useRef(a);return i.useEffect(()=>{b.current=a}),i.useMemo(()=>(...a)=>b.current?.(...a),[])}var z="dismissableLayer.update",A=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),B=i.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:j,onDismiss:m,...n}=a,o=i.useContext(A),[p,r]=i.useState(null),s=p?.ownerDocument??globalThis?.document,[,t]=i.useState({}),u=(0,l.s)(b,a=>r(a)),v=Array.from(o.layers),[w]=[...o.layersWithOutsidePointerEventsDisabled].slice(-1),x=v.indexOf(w),B=p?v.indexOf(p):-1,E=o.layersWithOutsidePointerEventsDisabled.size>0,F=B>=x,G=function(a,b=globalThis?.document){let c=y(a),d=i.useRef(!1),e=i.useRef(()=>{});return i.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){D("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...o.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),j?.(a),a.defaultPrevented||m?.())},s),H=function(a,b=globalThis?.document){let c=y(a),d=i.useRef(!1);return i.useEffect(()=>{let a=a=>{a.target&&!d.current&&D("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...o.branches].some(a=>a.contains(b))&&(h?.(a),j?.(a),a.defaultPrevented||m?.())},s);return!function(a,b=globalThis?.document){let c=y(a);i.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{B===o.layers.size-1&&(d?.(a),!a.defaultPrevented&&m&&(a.preventDefault(),m()))},s),i.useEffect(()=>{if(p)return c&&(0===o.layersWithOutsidePointerEventsDisabled.size&&(e=s.body.style.pointerEvents,s.body.style.pointerEvents="none"),o.layersWithOutsidePointerEventsDisabled.add(p)),o.layers.add(p),C(),()=>{c&&1===o.layersWithOutsidePointerEventsDisabled.size&&(s.body.style.pointerEvents=e)}},[p,s,c,o]),i.useEffect(()=>()=>{p&&(o.layers.delete(p),o.layersWithOutsidePointerEventsDisabled.delete(p),C())},[p,o]),i.useEffect(()=>{let a=()=>t({});return document.addEventListener(z,a),()=>document.removeEventListener(z,a)},[]),(0,g.jsx)(q.sG.div,{...n,ref:u,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:k(a.onFocusCapture,H.onFocusCapture),onBlurCapture:k(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:k(a.onPointerDownCapture,G.onPointerDownCapture)})});function C(){let a=new CustomEvent(z);document.dispatchEvent(a)}function D(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,q.hO)(e,f):e.dispatchEvent(f)}B.displayName="DismissableLayer",i.forwardRef((a,b)=>{let c=i.useContext(A),d=i.useRef(null),e=(0,l.s)(b,d);return i.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,g.jsx)(q.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var E=0;function F(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var G="focusScope.autoFocusOnMount",H="focusScope.autoFocusOnUnmount",I={bubbles:!1,cancelable:!0},J=i.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[j,k]=i.useState(null),m=y(e),n=y(f),o=i.useRef(null),p=(0,l.s)(b,a=>k(a)),r=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(d){let a=function(a){if(r.paused||!j)return;let b=a.target;j.contains(b)?o.current=b:M(o.current,{select:!0})},b=function(a){if(r.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||M(o.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&M(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,r.paused]),i.useEffect(()=>{if(j){N.add(r);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(G,I);j.addEventListener(G,m),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(M(d,{select:b}),document.activeElement!==c)return}(K(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&M(j))}return()=>{j.removeEventListener(G,m),setTimeout(()=>{let b=new CustomEvent(H,I);j.addEventListener(H,n),j.dispatchEvent(b),b.defaultPrevented||M(a??document.body,{select:!0}),j.removeEventListener(H,n),N.remove(r)},0)}}},[j,m,n,r]);let s=i.useCallback(a=>{if(!c&&!d||r.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=K(a);return[L(b,a),L(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&M(f,{select:!0})):(a.preventDefault(),c&&M(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,r.paused]);return(0,g.jsx)(q.sG.div,{tabIndex:-1,...h,ref:p,onKeyDown:s})});function K(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function L(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function M(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}J.displayName="FocusScope";var N=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=O(a,b)).unshift(b)},remove(b){a=O(a,b),a[0]?.resume()}}}();function O(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var P=j[" useId ".trim().toString()]||(()=>void 0),Q=0;function R(a){let[b,c]=i.useState(P());return n(()=>{a||c(a=>a??String(Q++))},[a]),a||(b?`radix-${b}`:"")}let S=["top","right","bottom","left"],T=Math.min,U=Math.max,V=Math.round,W=Math.floor,X=a=>({x:a,y:a}),Y={left:"right",right:"left",bottom:"top",top:"bottom"},Z={start:"end",end:"start"};function $(a,b){return"function"==typeof a?a(b):a}function _(a){return a.split("-")[0]}function aa(a){return a.split("-")[1]}function ab(a){return"x"===a?"y":"x"}function ac(a){return"y"===a?"height":"width"}let ad=new Set(["top","bottom"]);function ae(a){return ad.has(_(a))?"y":"x"}function af(a){return a.replace(/start|end/g,a=>Z[a])}let ag=["left","right"],ah=["right","left"],ai=["top","bottom"],aj=["bottom","top"];function ak(a){return a.replace(/left|right|bottom|top/g,a=>Y[a])}function al(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function am(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function an(a,b,c){let d,{reference:e,floating:f}=a,g=ae(b),h=ab(ae(b)),i=ac(h),j=_(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(aa(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let ao=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=an(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=an(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function ap(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=$(b,a),o=al(n),p=h[m?"floating"===l?"reference":"floating":l],q=am(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=am(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function aq(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function ar(a){return S.some(b=>a[b]>=0)}let as=new Set(["left","top"]);async function at(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=_(c),h=aa(c),i="y"===ae(c),j=as.has(g)?-1:1,k=f&&i?-1:1,l=$(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function au(){return"undefined"!=typeof window}function av(a){return ay(a)?(a.nodeName||"").toLowerCase():"#document"}function aw(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function ax(a){var b;return null==(b=(ay(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function ay(a){return!!au()&&(a instanceof Node||a instanceof aw(a).Node)}function az(a){return!!au()&&(a instanceof Element||a instanceof aw(a).Element)}function aA(a){return!!au()&&(a instanceof HTMLElement||a instanceof aw(a).HTMLElement)}function aB(a){return!!au()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof aw(a).ShadowRoot)}let aC=new Set(["inline","contents"]);function aD(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aO(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!aC.has(e)}let aE=new Set(["table","td","th"]),aF=[":popover-open",":modal"];function aG(a){return aF.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aH=["transform","translate","scale","rotate","perspective"],aI=["transform","translate","scale","rotate","perspective","filter"],aJ=["paint","layout","strict","content"];function aK(a){let b=aL(),c=az(a)?aO(a):a;return aH.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aI.some(a=>(c.willChange||"").includes(a))||aJ.some(a=>(c.contain||"").includes(a))}function aL(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aM=new Set(["html","body","#document"]);function aN(a){return aM.has(av(a))}function aO(a){return aw(a).getComputedStyle(a)}function aP(a){return az(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aQ(a){if("html"===av(a))return a;let b=a.assignedSlot||a.parentNode||aB(a)&&a.host||ax(a);return aB(b)?b.host:b}function aR(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aQ(b);return aN(c)?b.ownerDocument?b.ownerDocument.body:b.body:aA(c)&&aD(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=aw(e);if(f){let a=aS(g);return b.concat(g,g.visualViewport||[],aD(e)?e:[],a&&c?aR(a):[])}return b.concat(e,aR(e,[],c))}function aS(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aT(a){let b=aO(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=aA(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=V(c)!==f||V(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aU(a){return az(a)?a:a.contextElement}function aV(a){let b=aU(a);if(!aA(b))return X(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aT(b),g=(f?V(c.width):c.width)/d,h=(f?V(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let aW=X(0);function aX(a){let b=aw(a);return aL()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aW}function aY(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aU(a),h=X(1);b&&(d?az(d)&&(h=aV(d)):h=aV(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===aw(g))&&e)?aX(g):X(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=aw(g),b=d&&az(d)?aw(d):d,c=a,e=aS(c);for(;e&&d&&b!==c;){let a=aV(e),b=e.getBoundingClientRect(),d=aO(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aS(c=aw(e))}}return am({width:l,height:m,x:j,y:k})}function aZ(a,b){let c=aP(a).scrollLeft;return b?b.left+c:aY(ax(a)).left+c}function a$(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aZ(a,d)),y:d.top+b.scrollTop}}let a_=new Set(["absolute","fixed"]);function a0(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=aw(a),d=ax(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aL();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=ax(a),c=aP(a),d=a.ownerDocument.body,e=U(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=U(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aZ(a),h=-c.scrollTop;return"rtl"===aO(d).direction&&(g+=U(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(ax(a));else if(az(b))d=function(a,b){let c=aY(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=aA(a)?aV(a):X(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aX(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return am(d)}function a1(a){return"static"===aO(a).position}function a2(a,b){if(!aA(a)||"fixed"===aO(a).position)return null;if(b)return b(a);let c=a.offsetParent;return ax(a)===c&&(c=c.ownerDocument.body),c}function a3(a,b){var c;let d=aw(a);if(aG(a))return d;if(!aA(a)){let b=aQ(a);for(;b&&!aN(b);){if(az(b)&&!a1(b))return b;b=aQ(b)}return d}let e=a2(a,b);for(;e&&(c=e,aE.has(av(c)))&&a1(e);)e=a2(e,b);return e&&aN(e)&&a1(e)&&!aK(e)?d:e||function(a){let b=aQ(a);for(;aA(b)&&!aN(b);){if(aK(b))return b;if(aG(b))break;b=aQ(b)}return null}(a)||d}let a4=async function(a){let b=this.getOffsetParent||a3,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=aA(b),e=ax(b),f="fixed"===c,g=aY(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=X(0);if(d||!d&&!f)if(("body"!==av(b)||aD(e))&&(h=aP(b)),d){let a=aY(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aZ(e));f&&!d&&e&&(i.x=aZ(e));let j=!e||d||f?X(0):a$(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a5={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=ax(d),h=!!b&&aG(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=X(1),k=X(0),l=aA(d);if((l||!l&&!f)&&(("body"!==av(d)||aD(g))&&(i=aP(d)),aA(d))){let a=aY(d);j=aV(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?X(0):a$(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:ax,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aG(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aR(a,[],!1).filter(a=>az(a)&&"body"!==av(a)),e=null,f="fixed"===aO(a).position,g=f?aQ(a):a;for(;az(g)&&!aN(g);){let b=aO(g),c=aK(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&a_.has(e.position)||aD(g)&&!c&&function a(b,c){let d=aQ(b);return!(d===c||!az(d)||aN(d))&&("fixed"===aO(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aQ(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=a0(b,c,e);return a.top=U(d.top,a.top),a.right=T(d.right,a.right),a.bottom=T(d.bottom,a.bottom),a.left=U(d.left,a.left),a},a0(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a3,getElementRects:a4,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aT(a);return{width:b,height:c}},getScale:aV,isElement:az,isRTL:function(a){return"rtl"===aO(a).direction}};function a6(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let a7=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=$(a,b)||{};if(null==j)return{};let l=al(k),m={x:c,y:d},n=ab(ae(e)),o=ac(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=T(l[q?"top":"left"],w),y=T(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=U(x,T(A,z)),C=!i.arrow&&null!=aa(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a8=c(51215),a9="undefined"!=typeof document?i.useLayoutEffect:function(){};function ba(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ba(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ba(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function bb(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function bc(a,b){let c=bb(a);return Math.round(b*c)/c}function bd(a){let b=i.useRef(a);return a9(()=>{b.current=a}),b}var be=i.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,g.jsx)(q.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,g.jsx)("polygon",{points:"0,0 30,0 15,10"})})});be.displayName="Arrow";var bf="Popper",[bg,bh]=m(bf),[bi,bj]=bg(bf),bk=a=>{let{__scopePopper:b,children:c}=a,[d,e]=i.useState(null);return(0,g.jsx)(bi,{scope:b,anchor:d,onAnchorChange:e,children:c})};bk.displayName=bf;var bl="PopperAnchor",bm=i.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=bj(bl,c),h=i.useRef(null),j=(0,l.s)(b,h);return i.useEffect(()=>{f.onAnchorChange(d?.current||h.current)}),d?null:(0,g.jsx)(q.sG.div,{...e,ref:j})});bm.displayName=bl;var bn="PopperContent",[bo,bp]=bg(bn),bq=i.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:h=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:m=[],collisionPadding:o=0,sticky:p="partial",hideWhenDetached:r=!1,updatePositionStrategy:s="optimized",onPlaced:t,...u}=a,v=bj(bn,c),[w,x]=i.useState(null),z=(0,l.s)(b,a=>x(a)),[A,B]=i.useState(null),C=function(a){let[b,c]=i.useState(void 0);return n(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(A),D=C?.width??0,E=C?.height??0,F="number"==typeof o?o:{top:0,right:0,bottom:0,left:0,...o},G=Array.isArray(m)?m:[m],H=G.length>0,I={padding:F,boundary:G.filter(bu),altBoundary:H},{refs:J,floatingStyles:K,placement:L,isPositioned:M,middlewareData:N}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:g}={},transform:h=!0,whileElementsMounted:j,open:k}=a,[l,m]=i.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=i.useState(d);ba(n,d)||o(d);let[p,q]=i.useState(null),[r,s]=i.useState(null),t=i.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=i.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=f||p,w=g||r,x=i.useRef(null),y=i.useRef(null),z=i.useRef(l),A=null!=j,B=bd(j),C=bd(e),D=bd(k),E=i.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:a5,...c},f={...e.platform,_c:d};return ao(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!ba(z.current,b)&&(z.current=b,a8.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);a9(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=i.useRef(!1);a9(()=>(F.current=!0,()=>{F.current=!1}),[]),a9(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=i.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=i.useMemo(()=>({reference:v,floating:w}),[v,w]),I=i.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=bc(H.floating,l.x),d=bc(H.floating,l.y);return h?{...a,transform:"translate("+b+"px, "+d+"px)",...bb(H.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,h,H.floating,l.x,l.y]);return i.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aU(a),l=f||g?[...k?aR(k):[],...aR(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=ax(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=W(l),p=W(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-W(e.clientHeight-(l+n))+"px "+-W(k)+"px",threshold:U(0,T(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||a6(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aY(a):null;return j&&function b(){let d=aY(a);p&&!a6(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===s}),elements:{reference:v.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await at(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+E,alignmentAxis:h}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=$(a,b),j={x:c,y:d},k=await ap(b,i),l=ae(_(e)),m=ab(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=U(c,T(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=U(c,T(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=$(a,b),k={x:c,y:d},l=ae(e),m=ab(l),n=k[m],o=k[l],p=$(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=as.has(_(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=$(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=_(h),v=ae(k),w=_(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[ak(k)]:function(a){let b=ak(a);return[af(a),b,af(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=aa(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?ah:ag;return b?ag:ah;case"left":case"right":return b?ai:aj;default:return[]}}(_(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(af)))),f}(k,s,r,x));let A=[k,...y],B=await ap(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=aa(a),e=ab(ae(a)),f=ac(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=ak(g)),[g,ak(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ae(b)||D.every(a=>a.overflows[0]>0&&ae(a.placement)===v)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ae(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=$(a,b),m=await ap(b,l),n=_(g),o=aa(g),p="y"===ae(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=T(r-m[e],s),v=T(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=U(m.left,0),b=U(m.right,0),c=U(m.top,0),d=U(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:U(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:U(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),A&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?a7({element:c.current,padding:d}).fn(b):{}:c?a7({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:A,padding:j}),bv({arrowWidth:D,arrowHeight:E}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=$(a,b);switch(d){case"referenceHidden":{let a=aq(await ap(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:ar(a)}}}case"escaped":{let a=aq(await ap(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:ar(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[O,P]=bw(L),Q=y(t);n(()=>{M&&Q?.()},[M,Q]);let R=N.arrow?.x,S=N.arrow?.y,V=N.arrow?.centerOffset!==0,[X,Y]=i.useState();return n(()=>{w&&Y(window.getComputedStyle(w).zIndex)},[w]),(0,g.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:M?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,g.jsx)(bo,{scope:c,placedSide:O,onArrowChange:B,arrowX:R,arrowY:S,shouldHideArrow:V,children:(0,g.jsx)(q.sG.div,{"data-side":O,"data-align":P,...u,ref:z,style:{...u.style,animation:M?void 0:"none"}})})})});bq.displayName=bn;var br="PopperArrow",bs={top:"bottom",right:"left",bottom:"top",left:"right"},bt=i.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bp(br,c),f=bs[e.placedSide];return(0,g.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,g.jsx)(be,{...d,ref:b,style:{...d.style,display:"block"}})})});function bu(a){return null!==a}bt.displayName=br;var bv=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bw(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bw(a){let[b,c="center"]=a.split("-");return[b,c]}var bx=i.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=i.useState(!1);n(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?a8.createPortal((0,g.jsx)(q.sG.div,{...d,ref:b}),h):null});bx.displayName="Portal";var by=a=>{let{present:b,children:c}=a,d=function(a){var b,c;let[d,e]=i.useState(),f=i.useRef(null),g=i.useRef(a),h=i.useRef("none"),[j,k]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((a,b)=>c[a][b]??a,b));return i.useEffect(()=>{let a=bz(f.current);h.current="mounted"===j?a:"none"},[j]),n(()=>{let b=f.current,c=g.current;if(c!==a){let d=h.current,e=bz(b);a?k("MOUNT"):"none"===e||b?.display==="none"?k("UNMOUNT"):c&&d!==e?k("ANIMATION_OUT"):k("UNMOUNT"),g.current=a}},[a,k]),n(()=>{if(d){let a,b=d.ownerDocument.defaultView??window,c=c=>{let e=bz(f.current).includes(c.animationName);if(c.target===d&&e&&(k("ANIMATION_END"),!g.current)){let c=d.style.animationFillMode;d.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===d.style.animationFillMode&&(d.style.animationFillMode=c)})}},e=a=>{a.target===d&&(h.current=bz(f.current))};return d.addEventListener("animationstart",e),d.addEventListener("animationcancel",c),d.addEventListener("animationend",c),()=>{b.clearTimeout(a),d.removeEventListener("animationstart",e),d.removeEventListener("animationcancel",c),d.removeEventListener("animationend",c)}}k("ANIMATION_END")},[d,k]),{isPresent:["mounted","unmountSuspended"].includes(j),ref:i.useCallback(a=>{f.current=a?getComputedStyle(a):null,e(a)},[])}}(b),e="function"==typeof c?c({present:d.isPresent}):i.Children.only(c),f=(0,l.s)(d.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(e));return"function"==typeof c||d.isPresent?i.cloneElement(e,{ref:f}):null};function bz(a){return a?.animationName||"none"}by.displayName="Presence";var bA="rovingFocusGroup.onEntryFocus",bB={bubbles:!1,cancelable:!0},bC="RovingFocusGroup",[bD,bE,bF]=s(bC),[bG,bH]=m(bC,[bF]),[bI,bJ]=bG(bC),bK=i.forwardRef((a,b)=>(0,g.jsx)(bD.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(bD.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(bL,{...a,ref:b})})}));bK.displayName=bC;var bL=i.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:e=!1,dir:f,currentTabStopId:h,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:m,onEntryFocus:n,preventScrollOnEntryFocus:o=!1,...r}=a,s=i.useRef(null),t=(0,l.s)(b,s),u=x(f),[v,w]=p({prop:h,defaultProp:j??null,onChange:m,caller:bC}),[z,A]=i.useState(!1),B=y(n),C=bE(c),D=i.useRef(!1),[E,F]=i.useState(0);return i.useEffect(()=>{let a=s.current;if(a)return a.addEventListener(bA,B),()=>a.removeEventListener(bA,B)},[B]),(0,g.jsx)(bI,{scope:c,orientation:d,dir:u,loop:e,currentTabStopId:v,onItemFocus:i.useCallback(a=>w(a),[w]),onItemShiftTab:i.useCallback(()=>A(!0),[]),onFocusableItemAdd:i.useCallback(()=>F(a=>a+1),[]),onFocusableItemRemove:i.useCallback(()=>F(a=>a-1),[]),children:(0,g.jsx)(q.sG.div,{tabIndex:z||0===E?-1:0,"data-orientation":d,...r,ref:t,style:{outline:"none",...a.style},onMouseDown:k(a.onMouseDown,()=>{D.current=!0}),onFocus:k(a.onFocus,a=>{let b=!D.current;if(a.target===a.currentTarget&&b&&!z){let b=new CustomEvent(bA,bB);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=C().filter(a=>a.focusable);bP([a.find(a=>a.active),a.find(a=>a.id===v),...a].filter(Boolean).map(a=>a.ref.current),o)}}D.current=!1}),onBlur:k(a.onBlur,()=>A(!1))})})}),bM="RovingFocusGroupItem",bN=i.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:e=!1,tabStopId:f,children:h,...j}=a,l=R(),m=f||l,n=bJ(bM,c),o=n.currentTabStopId===m,p=bE(c),{onFocusableItemAdd:r,onFocusableItemRemove:s,currentTabStopId:t}=n;return i.useEffect(()=>{if(d)return r(),()=>s()},[d,r,s]),(0,g.jsx)(bD.ItemSlot,{scope:c,id:m,focusable:d,active:e,children:(0,g.jsx)(q.sG.span,{tabIndex:o?0:-1,"data-orientation":n.orientation,...j,ref:b,onMouseDown:k(a.onMouseDown,a=>{d?n.onItemFocus(m):a.preventDefault()}),onFocus:k(a.onFocus,()=>n.onItemFocus(m)),onKeyDown:k(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void n.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return bO[e]}(a,n.orientation,n.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=p().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=n.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>bP(c))}}),children:"function"==typeof h?h({isCurrentTabStop:o,hasTabStop:null!=t}):h})})});bN.displayName=bM;var bO={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function bP(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var bQ=new WeakMap,bR=new WeakMap,bS={},bT=0,bU=function(a){return a&&(a.host||bU(a.parentNode))},bV=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bU(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bS[c]||(bS[c]=new WeakMap);var f=bS[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bQ.get(a)||0)+1,j=(f.get(a)||0)+1;bQ.set(a,i),f.set(a,j),g.push(a),1===i&&e&&bR.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bT++,function(){g.forEach(function(a){var b=bQ.get(a)-1,e=f.get(a)-1;bQ.set(a,b),f.set(a,e),b||(bR.has(a)||a.removeAttribute(d),bR.delete(a)),e||a.removeAttribute(c)}),--bT||(bQ=new WeakMap,bQ=new WeakMap,bR=new WeakMap,bS={})}},bW=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bV(d,e,c,"aria-hidden")):function(){return null}},bX=function(){return(bX=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bY(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bZ=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),b$="width-before-scroll-bar";function b_(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var b0="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,b1=new WeakMap;function b2(a){return a}var b3=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=b2),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bX({async:!0,ssr:!1},a),e}(),b4=function(){},b5=i.forwardRef(function(a,b){var c,d,e,f,g=i.useRef(null),h=i.useState({onScrollCapture:b4,onWheelCapture:b4,onTouchMoveCapture:b4}),j=h[0],k=h[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bY(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[g,b],d=function(a){return c.forEach(function(b){return b_(b,a)})},(e=(0,i.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,b0(function(){var a=b1.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||b_(a,null)}),d.forEach(function(a){b.has(a)||b_(a,e)})}b1.set(f,c)},[c]),f),A=bX(bX({},y),j);return i.createElement(i.Fragment,null,p&&i.createElement(r,{sideCar:b3,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:g,gapMode:x}),l?i.cloneElement(i.Children.only(m),bX(bX({},A),{ref:z})):i.createElement(void 0===w?"div":w,bX({},A,{className:n,ref:z}),m))});b5.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b5.classNames={fullWidth:b$,zeroRight:bZ};var b6=function(a){var b=a.sideCar,c=bY(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return i.createElement(d,bX({},c))};b6.isSideCarExport=!0;var b7=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},b8=function(){var a=b7();return function(b,c){i.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},b9=function(){var a=b8();return function(b){return a(b.styles,b.dynamic),null}},ca={left:0,top:0,right:0,gap:0},cb=function(a){return parseInt(a||"",10)||0},cc=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[cb(c),cb(d),cb(e)]},cd=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return ca;var b=cc(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},ce=b9(),cf="data-scroll-locked",cg=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(cf,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bZ," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(b$," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bZ," .").concat(bZ," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(b$," .").concat(b$," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(cf,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},ch=function(){var a=parseInt(document.body.getAttribute(cf)||"0",10);return isFinite(a)?a:0},ci=function(){i.useEffect(function(){return document.body.setAttribute(cf,(ch()+1).toString()),function(){var a=ch()-1;a<=0?document.body.removeAttribute(cf):document.body.setAttribute(cf,a.toString())}},[])},cj=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;ci();var f=i.useMemo(function(){return cd(e)},[e]);return i.createElement(ce,{styles:cg(f,!b,e,c?"":"!important")})},ck=!1;if("undefined"!=typeof window)try{var cl=Object.defineProperty({},"passive",{get:function(){return ck=!0,!0}});window.addEventListener("test",cl,cl),window.removeEventListener("test",cl,cl)}catch(a){ck=!1}var cm=!!ck&&{passive:!1},cn=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},co=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),cp(a,d)){var e=cq(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},cp=function(a,b){return"v"===a?cn(b,"overflowY"):cn(b,"overflowX")},cq=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},cr=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=cq(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&cp(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},cs=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ct=function(a){return[a.deltaX,a.deltaY]},cu=function(a){return a&&"current"in a?a.current:a},cv=0,cw=[];let cx=(d=function(a){var b=i.useRef([]),c=i.useRef([0,0]),d=i.useRef(),e=i.useState(cv++)[0],f=i.useState(b9)[0],g=i.useRef(a);i.useEffect(function(){g.current=a},[a]),i.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(cu),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var h=i.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=cs(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=co(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=co(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return cr(n,b,a,"h"===n?i:j,!0)},[]),j=i.useCallback(function(a){if(cw.length&&cw[cw.length-1]===f){var c="deltaY"in a?ct(a):cs(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(cu).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?h(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=i.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=i.useCallback(function(a){c.current=cs(a),d.current=void 0},[]),m=i.useCallback(function(b){k(b.type,ct(b),b.target,h(b,a.lockRef.current))},[]),n=i.useCallback(function(b){k(b.type,cs(b),b.target,h(b,a.lockRef.current))},[]);i.useEffect(function(){return cw.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,cm),document.addEventListener("touchmove",j,cm),document.addEventListener("touchstart",l,cm),function(){cw=cw.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,cm),document.removeEventListener("touchmove",j,cm),document.removeEventListener("touchstart",l,cm)}},[]);var o=a.removeScrollBar,p=a.inert;return i.createElement(i.Fragment,null,p?i.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?i.createElement(cj,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},b3.useMedium(d),b6);var cy=i.forwardRef(function(a,b){return i.createElement(b5,bX({},a,{ref:b,sideCar:cx}))});cy.classNames=b5.classNames;var cz=["Enter"," "],cA=["ArrowUp","PageDown","End"],cB=["ArrowDown","PageUp","Home",...cA],cC={ltr:[...cz,"ArrowRight"],rtl:[...cz,"ArrowLeft"]},cD={ltr:["ArrowLeft"],rtl:["ArrowRight"]},cE="Menu",[cF,cG,cH]=s(cE),[cI,cJ]=m(cE,[cH,bh,bH]),cK=bh(),cL=bH(),[cM,cN]=cI(cE),[cO,cP]=cI(cE),cQ=a=>{let{__scopeMenu:b,open:c=!1,children:d,dir:e,onOpenChange:f,modal:h=!0}=a,j=cK(b),[k,l]=i.useState(null),m=i.useRef(!1),n=y(f),o=x(e);return i.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,g.jsx)(bk,{...j,children:(0,g.jsx)(cM,{scope:b,open:c,onOpenChange:n,content:k,onContentChange:l,children:(0,g.jsx)(cO,{scope:b,onClose:i.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:h,children:d})})})};cQ.displayName=cE;var cR=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=cK(c);return(0,g.jsx)(bm,{...e,...d,ref:b})});cR.displayName="MenuAnchor";var cS="MenuPortal",[cT,cU]=cI(cS,{forceMount:void 0}),cV=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=cN(cS,b);return(0,g.jsx)(cT,{scope:b,forceMount:c,children:(0,g.jsx)(by,{present:c||f.open,children:(0,g.jsx)(bx,{asChild:!0,container:e,children:d})})})};cV.displayName=cS;var cW="MenuContent",[cX,cY]=cI(cW),cZ=i.forwardRef((a,b)=>{let c=cU(cW,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=cN(cW,a.__scopeMenu),h=cP(cW,a.__scopeMenu);return(0,g.jsx)(cF.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(by,{present:d||f.open,children:(0,g.jsx)(cF.Slot,{scope:a.__scopeMenu,children:h.modal?(0,g.jsx)(c$,{...e,ref:b}):(0,g.jsx)(c_,{...e,ref:b})})})})}),c$=i.forwardRef((a,b)=>{let c=cN(cW,a.__scopeMenu),d=i.useRef(null),e=(0,l.s)(b,d);return i.useEffect(()=>{let a=d.current;if(a)return bW(a)},[]),(0,g.jsx)(c1,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:k(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),c_=i.forwardRef((a,b)=>{let c=cN(cW,a.__scopeMenu);return(0,g.jsx)(c1,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),c0=(0,r.TL)("MenuContent.ScrollLock"),c1=i.forwardRef((a,b)=>{let{__scopeMenu:c,loop:d=!1,trapFocus:e,onOpenAutoFocus:f,onCloseAutoFocus:h,disableOutsidePointerEvents:j,onEntryFocus:m,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:p,onInteractOutside:q,onDismiss:r,disableOutsideScroll:s,...t}=a,u=cN(cW,c),v=cP(cW,c),w=cK(c),x=cL(c),y=cG(c),[z,A]=i.useState(null),C=i.useRef(null),D=(0,l.s)(b,C,u.onContentChange),G=i.useRef(0),H=i.useRef(""),I=i.useRef(0),K=i.useRef(null),L=i.useRef("right"),M=i.useRef(0),N=s?cy:i.Fragment;i.useEffect(()=>()=>window.clearTimeout(G.current),[]),i.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??F()),document.body.insertAdjacentElement("beforeend",a[1]??F()),E++,()=>{1===E&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),E--}},[]);let O=i.useCallback(a=>L.current===K.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,K.current?.area),[]);return(0,g.jsx)(cX,{scope:c,searchRef:H,onItemEnter:i.useCallback(a=>{O(a)&&a.preventDefault()},[O]),onItemLeave:i.useCallback(a=>{O(a)||(C.current?.focus(),A(null))},[O]),onTriggerLeave:i.useCallback(a=>{O(a)&&a.preventDefault()},[O]),pointerGraceTimerRef:I,onPointerGraceIntentChange:i.useCallback(a=>{K.current=a},[]),children:(0,g.jsx)(N,{...s?{as:c0,allowPinchZoom:!0}:void 0,children:(0,g.jsx)(J,{asChild:!0,trapped:e,onMountAutoFocus:k(f,a=>{a.preventDefault(),C.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:h,children:(0,g.jsx)(B,{asChild:!0,disableOutsidePointerEvents:j,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:p,onInteractOutside:q,onDismiss:r,children:(0,g.jsx)(bK,{asChild:!0,...x,dir:v.dir,orientation:"vertical",loop:d,currentTabStopId:z,onCurrentTabStopIdChange:A,onEntryFocus:k(m,a=>{v.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,g.jsx)(bq,{role:"menu","aria-orientation":"vertical","data-state":ds(u.open),"data-radix-menu-content":"",dir:v.dir,...w,...t,ref:D,style:{outline:"none",...t.style},onKeyDown:k(t.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=H.current+a,c=y().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){H.current=b,window.clearTimeout(G.current),""!==b&&(G.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=C.current;if(a.target!==e||!cB.includes(a.key))return;a.preventDefault();let f=y().filter(a=>!a.disabled).map(a=>a.ref.current);cA.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:k(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(G.current),H.current="")}),onPointerMove:k(a.onPointerMove,dv(a=>{let b=a.target,c=M.current!==a.clientX;a.currentTarget.contains(b)&&c&&(L.current=a.clientX>M.current?"right":"left",M.current=a.clientX)}))})})})})})})});cZ.displayName=cW;var c2=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(q.sG.div,{role:"group",...d,ref:b})});c2.displayName="MenuGroup";var c3=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(q.sG.div,{...d,ref:b})});c3.displayName="MenuLabel";var c4="MenuItem",c5="menu.itemSelect",c6=i.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:d,...e}=a,f=i.useRef(null),h=cP(c4,a.__scopeMenu),j=cY(c4,a.__scopeMenu),m=(0,l.s)(b,f),n=i.useRef(!1);return(0,g.jsx)(c7,{...e,ref:m,disabled:c,onClick:k(a.onClick,()=>{let a=f.current;if(!c&&a){let b=new CustomEvent(c5,{bubbles:!0,cancelable:!0});a.addEventListener(c5,a=>d?.(a),{once:!0}),(0,q.hO)(a,b),b.defaultPrevented?n.current=!1:h.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),n.current=!0},onPointerUp:k(a.onPointerUp,a=>{n.current||a.currentTarget?.click()}),onKeyDown:k(a.onKeyDown,a=>{let b=""!==j.searchRef.current;c||b&&" "===a.key||cz.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});c6.displayName=c4;var c7=i.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:d=!1,textValue:e,...f}=a,h=cY(c4,c),j=cL(c),m=i.useRef(null),n=(0,l.s)(b,m),[o,p]=i.useState(!1),[r,s]=i.useState("");return i.useEffect(()=>{let a=m.current;a&&s((a.textContent??"").trim())},[f.children]),(0,g.jsx)(cF.ItemSlot,{scope:c,disabled:d,textValue:e??r,children:(0,g.jsx)(bN,{asChild:!0,...j,focusable:!d,children:(0,g.jsx)(q.sG.div,{role:"menuitem","data-highlighted":o?"":void 0,"aria-disabled":d||void 0,"data-disabled":d?"":void 0,...f,ref:n,onPointerMove:k(a.onPointerMove,dv(a=>{d?h.onItemLeave(a):(h.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:k(a.onPointerLeave,dv(a=>h.onItemLeave(a))),onFocus:k(a.onFocus,()=>p(!0)),onBlur:k(a.onBlur,()=>p(!1))})})})}),c8=i.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...e}=a;return(0,g.jsx)(dg,{scope:a.__scopeMenu,checked:c,children:(0,g.jsx)(c6,{role:"menuitemcheckbox","aria-checked":dt(c)?"mixed":c,...e,ref:b,"data-state":du(c),onSelect:k(e.onSelect,()=>d?.(!!dt(c)||!c),{checkForDefaultPrevented:!1})})})});c8.displayName="MenuCheckboxItem";var c9="MenuRadioGroup",[da,db]=cI(c9,{value:void 0,onValueChange:()=>{}}),dc=i.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=y(d);return(0,g.jsx)(da,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,g.jsx)(c2,{...e,ref:b})})});dc.displayName=c9;var dd="MenuRadioItem",de=i.forwardRef((a,b)=>{let{value:c,...d}=a,e=db(dd,a.__scopeMenu),f=c===e.value;return(0,g.jsx)(dg,{scope:a.__scopeMenu,checked:f,children:(0,g.jsx)(c6,{role:"menuitemradio","aria-checked":f,...d,ref:b,"data-state":du(f),onSelect:k(d.onSelect,()=>e.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});de.displayName=dd;var df="MenuItemIndicator",[dg,dh]=cI(df,{checked:!1}),di=i.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=dh(df,c);return(0,g.jsx)(by,{present:d||dt(f.checked)||!0===f.checked,children:(0,g.jsx)(q.sG.span,{...e,ref:b,"data-state":du(f.checked)})})});di.displayName=df;var dj=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(q.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});dj.displayName="MenuSeparator";var dk=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=cK(c);return(0,g.jsx)(bt,{...e,...d,ref:b})});dk.displayName="MenuArrow";var[dl,dm]=cI("MenuSub"),dn="MenuSubTrigger",dp=i.forwardRef((a,b)=>{let c=cN(dn,a.__scopeMenu),d=cP(dn,a.__scopeMenu),e=dm(dn,a.__scopeMenu),f=cY(dn,a.__scopeMenu),h=i.useRef(null),{pointerGraceTimerRef:j,onPointerGraceIntentChange:m}=f,n={__scopeMenu:a.__scopeMenu},o=i.useCallback(()=>{h.current&&window.clearTimeout(h.current),h.current=null},[]);return i.useEffect(()=>o,[o]),i.useEffect(()=>{let a=j.current;return()=>{window.clearTimeout(a),m(null)}},[j,m]),(0,g.jsx)(cR,{asChild:!0,...n,children:(0,g.jsx)(c7,{id:e.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":e.contentId,"data-state":ds(c.open),...a,ref:(0,l.t)(b,e.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:k(a.onPointerMove,dv(b=>{f.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||h.current||(f.onPointerGraceIntentChange(null),h.current=window.setTimeout(()=>{c.onOpenChange(!0),o()},100)))})),onPointerLeave:k(a.onPointerLeave,dv(a=>{o();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,g=b[e?"left":"right"],h=b[e?"right":"left"];f.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:g,y:b.top},{x:h,y:b.top},{x:h,y:b.bottom},{x:g,y:b.bottom}],side:d}),window.clearTimeout(j.current),j.current=window.setTimeout(()=>f.onPointerGraceIntentChange(null),300)}else{if(f.onTriggerLeave(a),a.defaultPrevented)return;f.onPointerGraceIntentChange(null)}})),onKeyDown:k(a.onKeyDown,b=>{let e=""!==f.searchRef.current;a.disabled||e&&" "===b.key||cC[d.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});dp.displayName=dn;var dq="MenuSubContent",dr=i.forwardRef((a,b)=>{let c=cU(cW,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=cN(cW,a.__scopeMenu),h=cP(cW,a.__scopeMenu),j=dm(dq,a.__scopeMenu),m=i.useRef(null),n=(0,l.s)(b,m);return(0,g.jsx)(cF.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(by,{present:d||f.open,children:(0,g.jsx)(cF.Slot,{scope:a.__scopeMenu,children:(0,g.jsx)(c1,{id:j.contentId,"aria-labelledby":j.triggerId,...e,ref:n,align:"start",side:"rtl"===h.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{h.isUsingKeyboardRef.current&&m.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:k(a.onFocusOutside,a=>{a.target!==j.trigger&&f.onOpenChange(!1)}),onEscapeKeyDown:k(a.onEscapeKeyDown,a=>{h.onClose(),a.preventDefault()}),onKeyDown:k(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=cD[h.dir].includes(a.key);b&&c&&(f.onOpenChange(!1),j.trigger?.focus(),a.preventDefault())})})})})})});function ds(a){return a?"open":"closed"}function dt(a){return"indeterminate"===a}function du(a){return dt(a)?"indeterminate":a?"checked":"unchecked"}function dv(a){return b=>"mouse"===b.pointerType?a(b):void 0}dr.displayName=dq;var dw="DropdownMenu",[dx,dy]=m(dw,[cJ]),dz=cJ(),[dA,dB]=dx(dw),dC=a=>{let{__scopeDropdownMenu:b,children:c,dir:d,open:e,defaultOpen:f,onOpenChange:h,modal:j=!0}=a,k=dz(b),l=i.useRef(null),[m,n]=p({prop:e,defaultProp:f??!1,onChange:h,caller:dw});return(0,g.jsx)(dA,{scope:b,triggerId:R(),triggerRef:l,contentId:R(),open:m,onOpenChange:n,onOpenToggle:i.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,g.jsx)(cQ,{...k,open:m,onOpenChange:n,dir:d,modal:j,children:c})})};dC.displayName=dw;var dD="DropdownMenuTrigger",dE=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...e}=a,f=dB(dD,c),h=dz(c);return(0,g.jsx)(cR,{asChild:!0,...h,children:(0,g.jsx)(q.sG.button,{type:"button",id:f.triggerId,"aria-haspopup":"menu","aria-expanded":f.open,"aria-controls":f.open?f.contentId:void 0,"data-state":f.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...e,ref:(0,l.t)(b,f.triggerRef),onPointerDown:k(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(f.onOpenToggle(),f.open||a.preventDefault())}),onKeyDown:k(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&f.onOpenToggle(),"ArrowDown"===a.key&&f.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});dE.displayName=dD;var dF=a=>{let{__scopeDropdownMenu:b,...c}=a,d=dz(b);return(0,g.jsx)(cV,{...d,...c})};dF.displayName="DropdownMenuPortal";var dG="DropdownMenuContent",dH=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dB(dG,c),f=dz(c),h=i.useRef(!1);return(0,g.jsx)(cZ,{id:e.contentId,"aria-labelledby":e.triggerId,...f,...d,ref:b,onCloseAutoFocus:k(a.onCloseAutoFocus,a=>{h.current||e.triggerRef.current?.focus(),h.current=!1,a.preventDefault()}),onInteractOutside:k(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!e.modal||d)&&(h.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dH.displayName=dG,i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(c2,{...e,...d,ref:b})}).displayName="DropdownMenuGroup";var dI=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(c3,{...e,...d,ref:b})});dI.displayName="DropdownMenuLabel";var dJ=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(c6,{...e,...d,ref:b})});dJ.displayName="DropdownMenuItem";var dK=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(c8,{...e,...d,ref:b})});dK.displayName="DropdownMenuCheckboxItem";var dL=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(dc,{...e,...d,ref:b})});dL.displayName="DropdownMenuRadioGroup";var dM=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(de,{...e,...d,ref:b})});dM.displayName="DropdownMenuRadioItem";var dN=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(di,{...e,...d,ref:b})});dN.displayName="DropdownMenuItemIndicator";var dO=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(dj,{...e,...d,ref:b})});dO.displayName="DropdownMenuSeparator",i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(dk,{...e,...d,ref:b})}).displayName="DropdownMenuArrow";var dP=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(dp,{...e,...d,ref:b})});dP.displayName="DropdownMenuSubTrigger";var dQ=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dz(c);return(0,g.jsx)(dr,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dQ.displayName="DropdownMenuSubContent";var dR=c(62688);let dS=(0,dR.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),dT=(0,dR.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),dU=(0,dR.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var dV=c(96241);i.forwardRef(({className:a,inset:b,children:c,...d},e)=>(0,g.jsxs)(dP,{ref:e,className:(0,dV.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...d,children:[c,(0,g.jsx)(dS,{className:"ml-auto"})]})).displayName=dP.displayName,i.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dQ,{ref:c,className:(0,dV.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=dQ.displayName;let dW=i.forwardRef(({className:a,sideOffset:b=4,...c},d)=>(0,g.jsx)(dF,{children:(0,g.jsx)(dH,{ref:d,sideOffset:b,className:(0,dV.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));dW.displayName=dH.displayName,i.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(dJ,{ref:d,className:(0,dV.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c})).displayName=dJ.displayName,i.forwardRef(({className:a,children:b,checked:c,...d},e)=>(0,g.jsxs)(dK,{ref:e,className:(0,dV.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...d,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(dN,{children:(0,g.jsx)(dT,{className:"h-4 w-4"})})}),b]})).displayName=dK.displayName;let dX=i.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(dM,{ref:d,className:(0,dV.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(dN,{children:(0,g.jsx)(dU,{className:"h-2 w-2 fill-current"})})}),b]}));dX.displayName=dM.displayName,i.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(dI,{ref:d,className:(0,dV.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=dI.displayName,i.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dO,{ref:c,className:(0,dV.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=dO.displayName;let dY=(0,dR.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),dZ=(0,dR.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),d$=(0,dR.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]);var d_=c(10218);let d0=()=>{let[a,b]=(0,i.useState)(!1),{theme:c,setTheme:d}=(0,d_.D)();return((0,i.useEffect)(()=>{b(!0)},[]),a)?(0,g.jsxs)(dC,{children:[(0,g.jsx)(dE,{asChild:!0,children:(0,g.jsx)(h.$,{variant:"ghost",size:"sm",children:"light"===c?(0,g.jsx)(dY,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,g.jsx)(dZ,{size:16,className:"text-muted-foreground"},"dark"):(0,g.jsx)(d$,{size:16,className:"text-muted-foreground"},"system")})}),(0,g.jsx)(dW,{className:"w-content",align:"start",children:(0,g.jsxs)(dL,{value:c,onValueChange:a=>d(a),children:[(0,g.jsxs)(dX,{className:"flex gap-2",value:"light",children:[(0,g.jsx)(dY,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"Light"})]}),(0,g.jsxs)(dX,{className:"flex gap-2",value:"dark",children:[(0,g.jsx)(dZ,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"Dark"})]}),(0,g.jsxs)(dX,{className:"flex gap-2",value:"system",children:[(0,g.jsx)(d$,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"System"})]})]})})]}):null}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"32x32",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},47990:()=>{},49603:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("/Users/<USER>/Downloads/Coding/IBC/crybaby-2/node_modules/next/dist/client/image-component.js")},51358:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>j,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(37413),e=c(61120),f=c(66819);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b})).displayName="CardDescription";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));j.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},51789:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},54656:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/components/theme-switcher.tsx","ThemeSwitcher")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(70672),f=c.n(e),g=c(23392);c(82704);let h=process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:"http://localhost:3000",i={metadataBase:new URL(h),title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",keywords:["AI","image processing","emotion","transformation","tears"],authors:[{name:"Tears of the Left"}],creator:"Tears of the Left",publisher:"Tears of the Left",formatDetection:{email:!1,address:!1,telephone:!1},icons:{icon:[{url:"/favicon.ico"},{url:"/icon-192.png",sizes:"192x192",type:"image/png"},{url:"/icon-512.png",sizes:"512x512",type:"image/png"}],apple:[{url:"/apple-touch-icon.png",sizes:"180x180",type:"image/png"}]},manifest:"/site.webmanifest",openGraph:{type:"website",locale:"en_US",url:h,title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",siteName:"Tears of the Left"},twitter:{card:"summary_large_image",title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",creator:"@tearsoftheleft"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,className:"w-full h-full",children:(0,d.jsx)("body",{className:`${f().className} antialiased w-full min-h-screen`,children:(0,d.jsx)(g.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:a})})})}},63004:(a,b,c)=>{Promise.resolve().then(c.bind(c,40478)),Promise.resolve().then(c.t.bind(c,46533,23))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f,dS:()=>g});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(){if(!process.env.SUPABASE_SERVICE_KEY)throw Error("Missing SUPABASE_SERVICE_KEY environment variable - required for rate limiting")}process.env.SUPABASE_SERVICE_KEY},70099:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(72639),e=c(9131),f=c(49603),g=d._(c(32091));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},72639:(a,b,c)=>{"use strict";function d(a){return a&&a.__esModule?a:{default:a}}c.r(b),c.d(b,{_:()=>d})},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},82704:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91449:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v});var d=c(37413),e=c(61120);function f(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var g=function(a){let b=function(a){let b=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==e.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=f(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():f(a[b],null)}}}}(b,i):i),e.cloneElement(c,j)}return e.Children.count(c)>1?e.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=e.forwardRef((a,c)=>{let{children:f,...g}=a,h=e.Children.toArray(f),j=h.find(i);if(j){let a=j.props.children,f=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(b,{...g,ref:c,children:e.isValidElement(a)?e.cloneElement(a,void 0,f):null})}return(0,d.jsx)(b,{...g,ref:c,children:f})});return c.displayName=`${a}.Slot`,c}("Slot"),h=Symbol("radix.slottable");function i(a){return e.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===h}var j=c(75986);let k=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,l=j.$;var m=c(66819);let n=((a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return l(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=k(b)||k(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return l(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},h)=>(0,d.jsx)(e?g:"button",{className:(0,m.cn)(n({variant:b,size:c,className:a})),ref:h,...f}));o.displayName="Button";var p=c(51358),q=c(54656),r=c(28086),s=c(39916),t=c(70099),u=c.n(t);async function v(){let a=await (0,r.U)(),{data:{user:b}}=await a.auth.getUser();return b&&(0,s.redirect)("/editor"),(0,d.jsx)("main",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"w-full max-w-md space-y-8 fade-in",children:[(0,d.jsxs)("div",{className:"text-center space-y-4",children:[(0,d.jsx)("div",{className:"flex justify-center mb-4",children:(0,d.jsxs)("div",{className:"w-24 h-24 relative group",children:[(0,d.jsx)(u(),{src:"/logo.svg",alt:"Tears of the Left Logo",width:96,height:96,className:"w-full h-full object-contain animate-pulse-slow"}),(0,d.jsxs)("div",{className:"absolute inset-0 w-full h-full",children:[(0,d.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"0s"}}),(0,d.jsx)("div",{className:"absolute top-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"0.3s"}}),(0,d.jsx)("div",{className:"absolute top-1/2 right-0 transform translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"0.6s"}}),(0,d.jsx)("div",{className:"absolute bottom-1 right-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"0.9s"}}),(0,d.jsx)("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"1.2s"}}),(0,d.jsx)("div",{className:"absolute bottom-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"1.5s"}}),(0,d.jsx)("div",{className:"absolute top-1/2 left-0 transform -translate-x-1 -translate-y-1/2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"1.8s"}}),(0,d.jsx)("div",{className:"absolute top-1 left-2 w-2 h-2 bg-accent rounded-full animate-star-appear opacity-0",style:{animationDelay:"2.1s"}})]})]})}),(0,d.jsx)("h1",{className:"text-4xl font-bold text-foreground",children:"Tears of the Left"}),(0,d.jsx)("p",{className:"text-secondary-foreground",children:"Transform your images with AI"})]}),(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsx)(w,{})}),(0,d.jsx)("div",{className:"flex items-center justify-center",children:(0,d.jsx)(q.ThemeSwitcher,{})})]})})}function w(){return(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)(p.Zp,{className:"border-accent/30 bg-card/90 shadow-xl backdrop-blur-sm",children:[(0,d.jsx)(p.aR,{children:(0,d.jsx)(p.ZB,{className:"text-center text-xl font-bold text-foreground",children:"Welcome"})}),(0,d.jsx)(p.Wu,{className:"space-y-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)(o,{asChild:!0,variant:"outline",className:"h-12 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent text-foreground font-semibold transition-all duration-200 hover:scale-105",children:(0,d.jsx)("a",{href:"/auth/login",children:"Sign In"})}),(0,d.jsx)(o,{asChild:!0,className:"h-12 bg-primary hover:bg-primary/80 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 transform",children:(0,d.jsx)("a",{href:"/auth/sign-up",children:"Sign Up"})})]})})]})})}},91645:a=>{"use strict";a.exports=require("net")},93866:(a,b,c)=>{Promise.resolve().then(c.bind(c,23392))},94735:a=>{"use strict";a.exports=require("events")},96241:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}process.env.SUPABASE_SERVICE_KEY}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[73,127,410,811,17,223],()=>b(b.s=35949));module.exports=c})();