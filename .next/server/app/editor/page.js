(()=>{var a={};a.id=766,a.ids=[766],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11469:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},11997:a=>{"use strict";a.exports=require("punycode")},16189:(a,b,c)=>{"use strict";var d=c(65773);c.o(d,"useRouter")&&c.d(b,{useRouter:function(){return d.useRouter}})},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28086:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(34386),e=c(44999);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},28354:a=>{"use strict";a.exports=require("util")},29141:(a,b,c)=>{Promise.resolve().then(c.bind(c,60340))},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31261:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(14985),e=c(44953),f=c(46533),g=d._(c(1933));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},33618:(a,b,c)=>{Promise.resolve().then(c.bind(c,10218))},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39455:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["editor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,60181)),"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58126)),"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/editor/page",pathname:"/editor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/editor/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"32x32",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},47828:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(59522);function e(){return(0,d.createBrowserClient)("https://jtqmhihkqrnhorrgwbqp.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0cW1oaWhrcXJuaG9ycmd3YnFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc2NDM1MzIsImV4cCI6MjA2MzIxOTUzMn0.n5eYmesQDsoBHEwETqo4-nG_2M0H-jMf4aW4Hv_M1Fg")}},47990:()=>{},51789:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(70672),f=c.n(e),g=c(23392);c(82704);let h=process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:"http://localhost:3000",i={metadataBase:new URL(h),title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",keywords:["AI","image processing","emotion","transformation","tears"],authors:[{name:"Tears of the Left"}],creator:"Tears of the Left",publisher:"Tears of the Left",formatDetection:{email:!1,address:!1,telephone:!1},icons:{icon:[{url:"/favicon.ico"},{url:"/icon-192.png",sizes:"192x192",type:"image/png"},{url:"/icon-512.png",sizes:"512x512",type:"image/png"}],apple:[{url:"/apple-touch-icon.png",sizes:"180x180",type:"image/png"}]},manifest:"/site.webmanifest",openGraph:{type:"website",locale:"en_US",url:h,title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",siteName:"Tears of the Left"},twitter:{card:"summary_large_image",title:"Tears of the Left",description:"Transform your images with AI-powered emotional processing",creator:"@tearsoftheleft"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,className:"w-full h-full",children:(0,d.jsx)("body",{className:`${f().className} antialiased w-full min-h-screen`,children:(0,d.jsx)(g.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:a})})})}},58126:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(39916),f=c(28086);async function g({children:a}){let b=await (0,f.U)(),{data:c,error:g}=await b.auth.getClaims();return(g||!c?.claims)&&(0,e.redirect)("/auth/login"),(0,d.jsx)(d.Fragment,{children:a})}},60181:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/Coding/IBC/crybaby-2/app/editor/page.tsx","default")},60340:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>S});var d=c(60687),e=c(43210),f=c(24934),g=c(55192),h=c(62688);let i=(0,h.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),j=(0,h.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),k=(0,h.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),l=(0,h.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var m=c(16189),n=c(47828),o=c(31261),p=c.n(o),q=c(96241);function r({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,q.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}let s=(0,h.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),t=(0,h.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),u=(0,h.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),v=(0,h.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var w=c(51215);let x=Array(12).fill(0),y=({visible:a,className:b})=>e.createElement("div",{className:["sonner-loading-wrapper",b].filter(Boolean).join(" "),"data-visible":a},e.createElement("div",{className:"sonner-spinner"},x.map((a,b)=>e.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${b}`})))),z=e.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),A=e.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},e.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),B=e.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),C=e.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},e.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),D=e.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},e.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),E=1;class F{constructor(){this.subscribe=a=>(this.subscribers.push(a),()=>{let b=this.subscribers.indexOf(a);this.subscribers.splice(b,1)}),this.publish=a=>{this.subscribers.forEach(b=>b(a))},this.addToast=a=>{this.publish(a),this.toasts=[...this.toasts,a]},this.create=a=>{var b;let{message:c,...d}=a,e="number"==typeof(null==a?void 0:a.id)||(null==(b=a.id)?void 0:b.length)>0?a.id:E++,f=this.toasts.find(a=>a.id===e),g=void 0===a.dismissible||a.dismissible;return this.dismissedToasts.has(e)&&this.dismissedToasts.delete(e),f?this.toasts=this.toasts.map(b=>b.id===e?(this.publish({...b,...a,id:e,title:c}),{...b,...a,id:e,dismissible:g,title:c}):b):this.addToast({title:c,...d,dismissible:g,id:e}),e},this.dismiss=a=>(a?(this.dismissedToasts.add(a),requestAnimationFrame(()=>this.subscribers.forEach(b=>b({id:a,dismiss:!0})))):this.toasts.forEach(a=>{this.subscribers.forEach(b=>b({id:a.id,dismiss:!0}))}),a),this.message=(a,b)=>this.create({...b,message:a}),this.error=(a,b)=>this.create({...b,message:a,type:"error"}),this.success=(a,b)=>this.create({...b,type:"success",message:a}),this.info=(a,b)=>this.create({...b,type:"info",message:a}),this.warning=(a,b)=>this.create({...b,type:"warning",message:a}),this.loading=(a,b)=>this.create({...b,type:"loading",message:a}),this.promise=(a,b)=>{let c,d;if(!b)return;void 0!==b.loading&&(d=this.create({...b,promise:a,type:"loading",message:b.loading,description:"function"!=typeof b.description?b.description:void 0}));let f=Promise.resolve(a instanceof Function?a():a),g=void 0!==d,h=f.then(async a=>{if(c=["resolve",a],e.isValidElement(a))g=!1,this.create({id:d,type:"default",message:a});else if(H(a)&&!a.ok){g=!1;let c="function"==typeof b.error?await b.error(`HTTP error! status: ${a.status}`):b.error,f="function"==typeof b.description?await b.description(`HTTP error! status: ${a.status}`):b.description,h="object"!=typeof c||e.isValidElement(c)?{message:c}:c;this.create({id:d,type:"error",description:f,...h})}else if(a instanceof Error){g=!1;let c="function"==typeof b.error?await b.error(a):b.error,f="function"==typeof b.description?await b.description(a):b.description,h="object"!=typeof c||e.isValidElement(c)?{message:c}:c;this.create({id:d,type:"error",description:f,...h})}else if(void 0!==b.success){g=!1;let c="function"==typeof b.success?await b.success(a):b.success,f="function"==typeof b.description?await b.description(a):b.description,h="object"!=typeof c||e.isValidElement(c)?{message:c}:c;this.create({id:d,type:"success",description:f,...h})}}).catch(async a=>{if(c=["reject",a],void 0!==b.error){g=!1;let c="function"==typeof b.error?await b.error(a):b.error,f="function"==typeof b.description?await b.description(a):b.description,h="object"!=typeof c||e.isValidElement(c)?{message:c}:c;this.create({id:d,type:"error",description:f,...h})}}).finally(()=>{g&&(this.dismiss(d),d=void 0),null==b.finally||b.finally.call(b)}),i=()=>new Promise((a,b)=>h.then(()=>"reject"===c[0]?b(c[1]):a(c[1])).catch(b));return"string"!=typeof d&&"number"!=typeof d?{unwrap:i}:Object.assign(d,{unwrap:i})},this.custom=(a,b)=>{let c=(null==b?void 0:b.id)||E++;return this.create({jsx:a(c),id:c,...b}),c},this.getActiveToasts=()=>this.toasts.filter(a=>!this.dismissedToasts.has(a.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let G=new F,H=a=>a&&"object"==typeof a&&"ok"in a&&"boolean"==typeof a.ok&&"status"in a&&"number"==typeof a.status,I=Object.assign((a,b)=>{let c=(null==b?void 0:b.id)||E++;return G.addToast({title:a,...b,id:c}),c},{success:G.success,info:G.info,warning:G.warning,error:G.error,custom:G.custom,message:G.message,promise:G.promise,dismiss:G.dismiss,loading:G.loading},{getHistory:()=>G.toasts,getToasts:()=>G.getActiveToasts()});function J(a){return void 0!==a.label}function K(...a){return a.filter(Boolean).join(" ")}!function(a){if(!a||"undefined"==typeof document)return;let b=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.type="text/css",b.appendChild(c),c.styleSheet?c.styleSheet.cssText=a:c.appendChild(document.createTextNode(a))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let L=a=>{var b,c,d,f,g,h,i,j,k,l,m;let{invert:n,toast:o,unstyled:p,interacting:q,setHeights:r,visibleToasts:s,heights:t,index:u,toasts:v,expanded:w,removeToast:x,defaultRichColors:E,closeButton:F,style:G,cancelButtonStyle:H,actionButtonStyle:I,className:L="",descriptionClassName:M="",duration:N,position:O,gap:P,expandByDefault:Q,classNames:R,icons:S,closeButtonAriaLabel:T="Close toast"}=a,[U,V]=e.useState(null),[W,X]=e.useState(null),[Y,Z]=e.useState(!1),[$,_]=e.useState(!1),[aa,ab]=e.useState(!1),[ac,ad]=e.useState(!1),[ae,af]=e.useState(!1),[ag,ah]=e.useState(0),[ai,aj]=e.useState(0),ak=e.useRef(o.duration||N||4e3),al=e.useRef(null),am=e.useRef(null),an=0===u,ao=u+1<=s,ap=o.type,aq=!1!==o.dismissible,ar=o.className||"",as=o.descriptionClassName||"",at=e.useMemo(()=>t.findIndex(a=>a.toastId===o.id)||0,[t,o.id]),au=e.useMemo(()=>{var a;return null!=(a=o.closeButton)?a:F},[o.closeButton,F]),av=e.useMemo(()=>o.duration||N||4e3,[o.duration,N]),aw=e.useRef(0),ax=e.useRef(0),ay=e.useRef(0),az=e.useRef(null),[aA,aB]=O.split("-"),aC=e.useMemo(()=>t.reduce((a,b,c)=>c>=at?a:a+b.height,0),[t,at]),aD=(()=>{let[a,b]=e.useState(document.hidden);return e.useEffect(()=>{let a=()=>{b(document.hidden)};return document.addEventListener("visibilitychange",a),()=>window.removeEventListener("visibilitychange",a)},[]),a})(),aE=o.invert||n,aF="loading"===ap;ax.current=e.useMemo(()=>at*P+aC,[at,aC]),e.useEffect(()=>{ak.current=av},[av]),e.useEffect(()=>{Z(!0)},[]),e.useEffect(()=>{let a=am.current;if(a){let b=a.getBoundingClientRect().height;return aj(b),r(a=>[{toastId:o.id,height:b,position:o.position},...a]),()=>r(a=>a.filter(a=>a.toastId!==o.id))}},[r,o.id]),e.useLayoutEffect(()=>{if(!Y)return;let a=am.current,b=a.style.height;a.style.height="auto";let c=a.getBoundingClientRect().height;a.style.height=b,aj(c),r(a=>a.find(a=>a.toastId===o.id)?a.map(a=>a.toastId===o.id?{...a,height:c}:a):[{toastId:o.id,height:c,position:o.position},...a])},[Y,o.title,o.description,r,o.id,o.jsx,o.action,o.cancel]);let aG=e.useCallback(()=>{_(!0),ah(ax.current),r(a=>a.filter(a=>a.toastId!==o.id)),setTimeout(()=>{x(o)},200)},[o,x,r,ax]);e.useEffect(()=>{let a;if((!o.promise||"loading"!==ap)&&o.duration!==1/0&&"loading"!==o.type)return w||q||aD?(()=>{if(ay.current<aw.current){let a=new Date().getTime()-aw.current;ak.current=ak.current-a}ay.current=new Date().getTime()})():ak.current!==1/0&&(aw.current=new Date().getTime(),a=setTimeout(()=>{null==o.onAutoClose||o.onAutoClose.call(o,o),aG()},ak.current)),()=>clearTimeout(a)},[w,q,o,ap,aD,aG]),e.useEffect(()=>{o.delete&&(aG(),null==o.onDismiss||o.onDismiss.call(o,o))},[aG,o.delete]);let aH=o.icon||(null==S?void 0:S[ap])||(a=>{switch(a){case"success":return z;case"info":return B;case"warning":return A;case"error":return C;default:return null}})(ap);return e.createElement("li",{tabIndex:0,ref:am,className:K(L,ar,null==R?void 0:R.toast,null==o||null==(b=o.classNames)?void 0:b.toast,null==R?void 0:R.default,null==R?void 0:R[ap],null==o||null==(c=o.classNames)?void 0:c[ap]),"data-sonner-toast":"","data-rich-colors":null!=(l=o.richColors)?l:E,"data-styled":!(o.jsx||o.unstyled||p),"data-mounted":Y,"data-promise":!!o.promise,"data-swiped":ae,"data-removed":$,"data-visible":ao,"data-y-position":aA,"data-x-position":aB,"data-index":u,"data-front":an,"data-swiping":aa,"data-dismissible":aq,"data-type":ap,"data-invert":aE,"data-swipe-out":ac,"data-swipe-direction":W,"data-expanded":!!(w||Q&&Y),style:{"--index":u,"--toasts-before":u,"--z-index":v.length-u,"--offset":`${$?ag:ax.current}px`,"--initial-height":Q?"auto":`${ai}px`,...G,...o.style},onDragEnd:()=>{ab(!1),V(null),az.current=null},onPointerDown:a=>{2!==a.button&&!aF&&aq&&(al.current=new Date,ah(ax.current),a.target.setPointerCapture(a.pointerId),"BUTTON"!==a.target.tagName&&(ab(!0),az.current={x:a.clientX,y:a.clientY}))},onPointerUp:()=>{var a,b,c,d,e;if(ac||!aq)return;az.current=null;let f=Number((null==(a=am.current)?void 0:a.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),g=Number((null==(b=am.current)?void 0:b.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),h=new Date().getTime()-(null==(c=al.current)?void 0:c.getTime()),i="x"===U?f:g,j=Math.abs(i)/h;if(Math.abs(i)>=45||j>.11){ah(ax.current),null==o.onDismiss||o.onDismiss.call(o,o),"x"===U?X(f>0?"right":"left"):X(g>0?"down":"up"),aG(),ad(!0);return}null==(d=am.current)||d.style.setProperty("--swipe-amount-x","0px"),null==(e=am.current)||e.style.setProperty("--swipe-amount-y","0px"),af(!1),ab(!1),V(null)},onPointerMove:b=>{var c,d,e,f;if(!az.current||!aq||(null==(c=window.getSelection())?void 0:c.toString().length)>0)return;let g=b.clientY-az.current.y,h=b.clientX-az.current.x,i=null!=(f=a.swipeDirections)?f:function(a){let[b,c]=a.split("-"),d=[];return b&&d.push(b),c&&d.push(c),d}(O);!U&&(Math.abs(h)>1||Math.abs(g)>1)&&V(Math.abs(h)>Math.abs(g)?"x":"y");let j={x:0,y:0},k=a=>1/(1.5+Math.abs(a)/20);if("y"===U){if(i.includes("top")||i.includes("bottom"))if(i.includes("top")&&g<0||i.includes("bottom")&&g>0)j.y=g;else{let a=g*k(g);j.y=Math.abs(a)<Math.abs(g)?a:g}}else if("x"===U&&(i.includes("left")||i.includes("right")))if(i.includes("left")&&h<0||i.includes("right")&&h>0)j.x=h;else{let a=h*k(h);j.x=Math.abs(a)<Math.abs(h)?a:h}(Math.abs(j.x)>0||Math.abs(j.y)>0)&&af(!0),null==(d=am.current)||d.style.setProperty("--swipe-amount-x",`${j.x}px`),null==(e=am.current)||e.style.setProperty("--swipe-amount-y",`${j.y}px`)}},au&&!o.jsx&&"loading"!==ap?e.createElement("button",{"aria-label":T,"data-disabled":aF,"data-close-button":!0,onClick:aF||!aq?()=>{}:()=>{aG(),null==o.onDismiss||o.onDismiss.call(o,o)},className:K(null==R?void 0:R.closeButton,null==o||null==(d=o.classNames)?void 0:d.closeButton)},null!=(m=null==S?void 0:S.close)?m:D):null,(ap||o.icon||o.promise)&&null!==o.icon&&((null==S?void 0:S[ap])!==null||o.icon)?e.createElement("div",{"data-icon":"",className:K(null==R?void 0:R.icon,null==o||null==(f=o.classNames)?void 0:f.icon)},o.promise||"loading"===o.type&&!o.icon?o.icon||function(){var a,b;return(null==S?void 0:S.loading)?e.createElement("div",{className:K(null==R?void 0:R.loader,null==o||null==(b=o.classNames)?void 0:b.loader,"sonner-loader"),"data-visible":"loading"===ap},S.loading):e.createElement(y,{className:K(null==R?void 0:R.loader,null==o||null==(a=o.classNames)?void 0:a.loader),visible:"loading"===ap})}():null,"loading"!==o.type?aH:null):null,e.createElement("div",{"data-content":"",className:K(null==R?void 0:R.content,null==o||null==(g=o.classNames)?void 0:g.content)},e.createElement("div",{"data-title":"",className:K(null==R?void 0:R.title,null==o||null==(h=o.classNames)?void 0:h.title)},o.jsx?o.jsx:"function"==typeof o.title?o.title():o.title),o.description?e.createElement("div",{"data-description":"",className:K(M,as,null==R?void 0:R.description,null==o||null==(i=o.classNames)?void 0:i.description)},"function"==typeof o.description?o.description():o.description):null),e.isValidElement(o.cancel)?o.cancel:o.cancel&&J(o.cancel)?e.createElement("button",{"data-button":!0,"data-cancel":!0,style:o.cancelButtonStyle||H,onClick:a=>{J(o.cancel)&&aq&&(null==o.cancel.onClick||o.cancel.onClick.call(o.cancel,a),aG())},className:K(null==R?void 0:R.cancelButton,null==o||null==(j=o.classNames)?void 0:j.cancelButton)},o.cancel.label):null,e.isValidElement(o.action)?o.action:o.action&&J(o.action)?e.createElement("button",{"data-button":!0,"data-action":!0,style:o.actionButtonStyle||I,onClick:a=>{J(o.action)&&(null==o.action.onClick||o.action.onClick.call(o.action,a),a.defaultPrevented||aG())},className:K(null==R?void 0:R.actionButton,null==o||null==(k=o.classNames)?void 0:k.actionButton)},o.action.label):null)},M=e.forwardRef(function(a,b){let{invert:c,position:d="bottom-right",hotkey:f=["altKey","KeyT"],expand:g,closeButton:h,className:i,offset:j,mobileOffset:k,theme:l="light",richColors:m,duration:n,style:o,visibleToasts:p=3,toastOptions:q,dir:r="ltr",gap:s=14,icons:t,containerAriaLabel:u="Notifications"}=a,[v,x]=e.useState([]),y=e.useMemo(()=>Array.from(new Set([d].concat(v.filter(a=>a.position).map(a=>a.position)))),[v,d]),[z,A]=e.useState([]),[B,C]=e.useState(!1),[D,E]=e.useState(!1),[F,H]=e.useState("system"!==l?l:"light"),I=e.useRef(null),J=f.join("+").replace(/Key/g,"").replace(/Digit/g,""),K=e.useRef(null),M=e.useRef(!1),N=e.useCallback(a=>{x(b=>{var c;return(null==(c=b.find(b=>b.id===a.id))?void 0:c.delete)||G.dismiss(a.id),b.filter(({id:b})=>b!==a.id)})},[]);return e.useEffect(()=>G.subscribe(a=>{if(a.dismiss)return void requestAnimationFrame(()=>{x(b=>b.map(b=>b.id===a.id?{...b,delete:!0}:b))});setTimeout(()=>{w.flushSync(()=>{x(b=>{let c=b.findIndex(b=>b.id===a.id);return -1!==c?[...b.slice(0,c),{...b[c],...a},...b.slice(c+1)]:[a,...b]})})})}),[v]),e.useEffect(()=>{if("system"!==l)return void H(l);"system"===l&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?H("dark"):H("light"))},[l]),e.useEffect(()=>{v.length<=1&&C(!1)},[v]),e.useEffect(()=>{let a=a=>{var b,c;f.every(b=>a[b]||a.code===b)&&(C(!0),null==(c=I.current)||c.focus()),"Escape"===a.code&&(document.activeElement===I.current||(null==(b=I.current)?void 0:b.contains(document.activeElement)))&&C(!1)};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[f]),e.useEffect(()=>{if(I.current)return()=>{K.current&&(K.current.focus({preventScroll:!0}),K.current=null,M.current=!1)}},[I.current]),e.createElement("section",{ref:b,"aria-label":`${u} ${J}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},y.map((b,d)=>{var f;let[l,u]=b.split("-");return v.length?e.createElement("ol",{key:b,dir:"auto"===r?"ltr":r,tabIndex:-1,ref:I,className:i,"data-sonner-toaster":!0,"data-sonner-theme":F,"data-y-position":l,"data-x-position":u,style:{"--front-toast-height":`${(null==(f=z[0])?void 0:f.height)||0}px`,"--width":"356px","--gap":`${s}px`,...o,...function(a,b){let c={};return[a,b].forEach((a,b)=>{let d=1===b,e=d?"--mobile-offset":"--offset",f=d?"16px":"24px";function g(a){["top","right","bottom","left"].forEach(b=>{c[`${e}-${b}`]="number"==typeof a?`${a}px`:a})}"number"==typeof a||"string"==typeof a?g(a):"object"==typeof a?["top","right","bottom","left"].forEach(b=>{void 0===a[b]?c[`${e}-${b}`]=f:c[`${e}-${b}`]="number"==typeof a[b]?`${a[b]}px`:a[b]}):g(f)}),c}(j,k)},onBlur:a=>{M.current&&!a.currentTarget.contains(a.relatedTarget)&&(M.current=!1,K.current&&(K.current.focus({preventScroll:!0}),K.current=null))},onFocus:a=>{!(a.target instanceof HTMLElement&&"false"===a.target.dataset.dismissible)&&(M.current||(M.current=!0,K.current=a.relatedTarget))},onMouseEnter:()=>C(!0),onMouseMove:()=>C(!0),onMouseLeave:()=>{D||C(!1)},onDragEnd:()=>C(!1),onPointerDown:a=>{a.target instanceof HTMLElement&&"false"===a.target.dataset.dismissible||E(!0)},onPointerUp:()=>E(!1)},v.filter(a=>!a.position&&0===d||a.position===b).map((d,f)=>{var i,j;return e.createElement(L,{key:d.id,icons:t,index:f,toast:d,defaultRichColors:m,duration:null!=(i=null==q?void 0:q.duration)?i:n,className:null==q?void 0:q.className,descriptionClassName:null==q?void 0:q.descriptionClassName,invert:c,visibleToasts:p,closeButton:null!=(j=null==q?void 0:q.closeButton)?j:h,interacting:D,position:b,style:null==q?void 0:q.style,unstyled:null==q?void 0:q.unstyled,classNames:null==q?void 0:q.classNames,cancelButtonStyle:null==q?void 0:q.cancelButtonStyle,actionButtonStyle:null==q?void 0:q.actionButtonStyle,closeButtonAriaLabel:null==q?void 0:q.closeButtonAriaLabel,removeToast:N,toasts:v.filter(a=>a.position==d.position),heights:z.filter(a=>a.position==d.position),setHeights:A,expandByDefault:g,gap:s,expanded:B,swipeDirections:a.swipeDirections})})):null}))}),N=async(a,b)=>{try{console.log("\uD83D\uDD3D Starting image download:",{imageUrl:a,filename:b});let c=await fetch(a);if(!c.ok)throw Error(`Failed to fetch image: ${c.status} ${c.statusText}`);let d=await c.blob();console.log("\uD83D\uDCE6 Image blob created:",{size:d.size,type:d.type});let e=window.URL.createObjectURL(d),f=document.createElement("a");return f.href=e,f.download=b||`tears-of-the-left-${Date.now()}.png`,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(e),console.log("✅ Image download completed successfully"),!0}catch(a){return console.error("❌ Image download failed:",a),!1}},O=async a=>{try{if(console.log("\uD83D\uDCCB Copying text to clipboard:",{textLength:a.length}),navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(a),console.log("✅ Text copied using Clipboard API"),!0;{let b=document.createElement("textarea");b.value=a,b.style.position="fixed",b.style.left="-999999px",b.style.top="-999999px",document.body.appendChild(b),b.focus(),b.select();let c=document.execCommand("copy");if(document.body.removeChild(b),c)return console.log("✅ Text copied using execCommand fallback"),!0;throw Error("execCommand copy failed")}}catch(a){return console.error("❌ Text copy failed:",a),!1}},P=async a=>{try{if(console.log("\uD83D\uDDBC️ Copying image to clipboard:",{imageUrl:a}),!navigator.clipboard||!window.ClipboardItem)return console.log("⚠️ Clipboard API not available for images"),!1;let b=await fetch(a);if(!b.ok)throw Error(`Failed to fetch image: ${b.status}`);let c=await b.blob();console.log("\uD83D\uDCE6 Image blob for clipboard:",{size:c.size,type:c.type});let d=new ClipboardItem({[c.type]:c});return await navigator.clipboard.write([d]),console.log("✅ Image copied to clipboard successfully"),!0}catch(a){return console.error("❌ Image clipboard copy failed:",a),!1}},Q=async(a,b,c)=>{console.log("\uD83D\uDE80 Starting Twitter share process:",{imageUrl:a,text:b,filename:c});let d={success:!1,message:"",imageCopied:!1,imageDownloaded:!1,textCopied:!1,twitterOpened:!1};try{return console.log("\uD83D\uDCCB Step 1: Attempting image clipboard copy..."),d.imageCopied=await P(a),console.log("\uD83D\uDCBE Step 2: Downloading image as backup..."),d.imageDownloaded=await N(a,c),d.imageCopied||(console.log("\uD83D\uDCDD Step 3: Copying text to clipboard as fallback..."),d.textCopied=await O(b)),console.log("\uD83D\uDC26 Step 4: Opening Twitter Web Intent..."),d.twitterOpened=(a=>{try{console.log("\uD83D\uDC26 Opening Twitter Web Intent:",{textLength:a.length});let b=encodeURIComponent(a),c=`https://twitter.com/intent/tweet?text=${b}`;return console.log("\uD83D\uDD17 Twitter URL:",c),window.location.href=c,console.log("✅ Twitter intent opened successfully"),!0}catch(a){return console.error("❌ Twitter intent failed:",a),!1}})(b),d.imageCopied&&d.twitterOpened?(d.success=!0,d.message="Image copied to clipboard! Twitter opened with your text. Just paste the image in your tweet."):d.imageDownloaded&&d.twitterOpened?(d.success=!0,d.textCopied?d.message="Image downloaded and text copied! Twitter opened. Upload the downloaded image to your tweet.":d.message="Image downloaded! Twitter opened with your text. Upload the downloaded image to your tweet."):d.twitterOpened?(d.success=!0,d.message="Twitter opened with your text. Please manually save and upload the image."):(d.success=!1,d.message="Unable to open Twitter. Please copy your text and image manually."),console.log("\uD83C\uDFAF Twitter share result:",d),d}catch(a){return console.error("\uD83D\uDCA5 Twitter share process failed:",a),d.success=!1,d.message="An error occurred while sharing to Twitter. Please try again.",d}};function R({imageUrl:a,className:b}){let[c,h]=(0,e.useState)(""),[i,j]=(0,e.useState)(!1),k=async()=>{if(console.log("\uD83D\uDC26 TwitterPostSection: Starting share process"),!c.trim())return void I.error("Please enter some text for your tweet.");j(!0);try{let b=await Q(a,c,`tears-of-the-left-${Date.now()}.png`);b.success?I.success(b.message,{duration:5e3,action:b.imageCopied?{label:"Got it!",onClick:()=>I.dismiss()}:void 0}):I.error(b.message,{duration:7e3,action:{label:"Retry",onClick:()=>k()}})}catch(a){console.error("❌ TwitterPostSection: Share failed:",a),I.error("Failed to share to Twitter. Please try again.")}finally{j(!1)}},l=async()=>{try{await navigator.clipboard.writeText(c),I.success("Tweet text copied to clipboard!")}catch(a){console.error("Failed to copy text:",a),I.error("Failed to copy text to clipboard.")}};return(0,d.jsxs)(g.Zp,{className:`border-accent/30 bg-secondary/5 shadow-xl ${b}`,children:[(0,d.jsx)(g.aR,{className:"pb-4",children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,d.jsx)(s,{className:"h-5 w-5 text-accent"}),"Share to Twitter"]})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{htmlFor:"tweet-text",className:"text-sm font-medium text-foreground",children:"Tweet Text"}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:l,className:"h-6 px-2 text-xs",children:[(0,d.jsx)(t,{className:"h-3 w-3 mr-1"}),"Copy"]})]}),(0,d.jsx)(r,{id:"tweet-text",value:c,onChange:a=>h(a.target.value),placeholder:"What's happening?",className:"min-h-[100px] resize-none",maxLength:300})]}),(0,d.jsxs)("div",{className:"text-xs text-white p-3 bg-accent/5 rounded-lg border border-accent/20",children:[(0,d.jsx)("p",{className:"font-medium mb-1 text-white",children:"How it works:"}),(0,d.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-white",children:[(0,d.jsx)("li",{children:"Your image will be copied to clipboard (if supported)"}),(0,d.jsx)("li",{children:"Image will be downloaded as backup"}),(0,d.jsx)("li",{children:"Twitter will open with your text pre-filled"}),(0,d.jsx)("li",{children:"Paste or upload the image in your tweet"})]})]}),(0,d.jsx)(f.$,{onClick:k,disabled:i||!c.trim(),className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100 h-14",children:i?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u,{className:"h-5 w-5 mr-3 animate-spin"}),"Sharing to Twitter..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v,{className:"h-5 w-5 mr-3"}),"Share to Twitter"]})})]})]})}function S(){let[a,b]=(0,e.useState)(null),[c,h]=(0,e.useState)(!1),[o,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(null),[v,w]=(0,e.useState)(!1),x=(0,m.useRouter)(),y=(0,n.U)(),z=async()=>{await y.auth.signOut(),x.push("/")},A=async a=>{if(b(a),q(null),s(null),u(null),a)try{let b=new FormData;b.append("file",a);let c=await fetch("/api/upload",{method:"POST",body:b});if(!c.ok){let a=await c.json();throw Error(a.error||"Upload failed")}let d=await c.json();u(d.image)}catch(a){q(a instanceof Error?a.message:"Upload failed")}},B=async()=>{if(!t)return void q("Please upload an image first");h(!0),q(null),s(null);try{let a=await fetch("/api/process",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({imageData:t,prompt:"Retro cartoon illustration of a sad elderly man in a dark navy suit and teal necktie, large square glasses, single tear rolling down cheek. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture, simple background with soft abstract swirls in tan. Front-facing bust portrait, expressive arched eyebrows and downturned mouth. Clean vector aesthetic, high-resolution"})});if(!a.ok){let b=await a.json();throw Error(b.error||"Processing failed")}let b=await a.json();s(b)}catch(a){q(a instanceof Error?a.message:"Processing failed")}finally{h(!1)}},C=a&&!c;return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)("div",{className:"sticky top-0 z-50 w-full border-b border-accent/20 bg-background/80 backdrop-blur-sm",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-4 flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 relative",children:(0,d.jsx)(p(),{src:"/logo.svg",alt:"Tears of the Left Logo",width:40,height:40,className:"w-full h-full object-contain"})}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:"Tears of the Left"})]}),(0,d.jsxs)(f.$,{onClick:z,variant:"outline",size:"sm",className:"flex items-center gap-2 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent transition-all duration-200",children:[(0,d.jsx)(i,{className:"h-4 w-4"}),"Sign Out"]})]})}),(0,d.jsx)("div",{className:"w-full px-6 py-8",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8 fade-in",children:[(0,d.jsx)(g.Zp,{className:`border-2 border-dashed transition-all duration-300 hover:shadow-lg ${v?"border-accent bg-accent/10 shadow-lg scale-[1.02]":"border-secondary/30 bg-secondary/5"}`,onDragOver:a=>{a.preventDefault(),w(!0)},onDragLeave:a=>{a.preventDefault(),w(!1)},onDrop:a=>{a.preventDefault(),w(!1);let b=a.dataTransfer.files;if(b.length>0){let a=b[0];a.type.startsWith("image/")?A(a):q("Please drop an image file")}},children:(0,d.jsx)(g.Wu,{className:"p-8",children:a?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center",children:(0,d.jsx)(j,{className:"h-6 w-6 text-accent"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-foreground text-base",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-foreground/70",children:[(a.size/1024/1024).toFixed(2)," MB • Ready to transform"]})]})]}),(0,d.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>A(null),disabled:c,className:"border-accent/30 hover:bg-accent/10 text-foreground",children:"Change"})]}),(0,d.jsx)(f.$,{onClick:B,disabled:!C,className:"w-full h-14 text-lg bg-primary hover:bg-primary/90 text-primary-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",size:"lg",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3"}),"Creating tears..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k,{className:"h-6 w-6 mr-3"}),"Transform Image"]})})]}):(0,d.jsxs)("div",{className:"text-center space-y-6",children:[(0,d.jsx)("div",{className:"mx-auto w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110",children:(0,d.jsx)(j,{className:`h-10 w-10 transition-colors ${v?"text-accent":"text-muted-foreground"}`})}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-foreground",children:"Upload your image"}),(0,d.jsx)("p",{className:"text-foreground/80 text-base",children:v?"✨ Drop your image here":'Transform with the "Tears of the Left" effect'})]}),(0,d.jsx)("input",{type:"file",accept:"image/*",onChange:a=>A(a.target.files?.[0]||null),className:"hidden",id:"file-upload",disabled:c}),(0,d.jsx)(f.$,{asChild:!0,className:"bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200",disabled:c,children:(0,d.jsx)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:"Choose Image"})})]})})}),o&&(0,d.jsx)(g.Zp,{className:"border-destructive/50 bg-destructive/10 shadow-lg",children:(0,d.jsx)(g.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-3 text-center justify-center",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-destructive/20 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-destructive font-bold",children:"!"})}),(0,d.jsx)("p",{className:"text-destructive font-medium text-base",children:o})]})})}),c&&(0,d.jsx)(g.Zp,{className:"border-accent/30 bg-secondary/5 shadow-xl",children:(0,d.jsx)(g.Wu,{className:"p-8",children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-3",children:"Creating your masterpiece..."}),(0,d.jsx)("p",{className:"text-base text-foreground/80",children:"The AI is painting tears of emotion onto your image"})]}),(0,d.jsx)("div",{className:"relative rounded-xl overflow-hidden bg-accent/5 h-96 flex items-center justify-center border border-accent/20",children:(0,d.jsxs)("div",{className:"space-y-6 text-center",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"w-20 h-20 mx-auto relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 rounded-full bg-accent/30 animate-ping opacity-75"}),(0,d.jsx)("div",{className:"absolute inset-2 rounded-full bg-accent/50 animate-pulse"}),(0,d.jsx)("div",{className:"absolute inset-4 rounded-full bg-accent"})]}),(0,d.jsxs)("div",{className:"absolute -left-10 top-10 space-y-3",children:[(0,d.jsx)("div",{className:"w-3 h-4 bg-accent rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,d.jsx)("div",{className:"w-3 h-4 bg-accent/70 rounded-full animate-bounce opacity-70",style:{animationDelay:"0.2s"}}),(0,d.jsx)("div",{className:"w-3 h-4 bg-accent/50 rounded-full animate-bounce opacity-50",style:{animationDelay:"0.4s"}})]}),(0,d.jsxs)("div",{className:"absolute -right-10 top-10 space-y-3",children:[(0,d.jsx)("div",{className:"w-3 h-4 bg-accent/60 rounded-full animate-bounce",style:{animationDelay:"0.3s"}}),(0,d.jsx)("div",{className:"w-3 h-4 bg-accent/40 rounded-full animate-bounce opacity-60",style:{animationDelay:"0.5s"}})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("div",{className:"text-lg font-semibold text-foreground",children:"Transforming with emotion..."}),(0,d.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,d.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-3 h-3 bg-accent rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})})}),r&&!c&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-3",children:"✨ Your transformed image"}),(0,d.jsx)("p",{className:"text-base text-foreground/80",children:"The “Tears of the Left” effect has been applied"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:items-start",children:[(0,d.jsx)(g.Zp,{className:"border-accent/30 bg-secondary/5 shadow-xl h-full",children:(0,d.jsx)(g.Wu,{className:"p-8 h-full flex flex-col",children:(0,d.jsxs)("div",{className:"space-y-6 flex-1 flex flex-col",children:[(0,d.jsx)("div",{className:"relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-4 flex-1 flex items-center justify-center",children:(0,d.jsx)(p(),{src:r.editedImageUrl,alt:"Transformed image",width:1024,height:1024,className:"w-full h-auto max-h-96 object-contain mx-auto rounded-lg shadow-lg",unoptimized:!0})}),(0,d.jsxs)(f.$,{onClick:()=>{let a=document.createElement("a");a.href=r.editedImageUrl,a.download=`tears-of-the-left-${Date.now()}.png`,document.body.appendChild(a),a.click(),document.body.removeChild(a)},className:"w-full h-14 bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]",children:[(0,d.jsx)(l,{className:"h-5 w-5 mr-3"}),"Download Image"]})]})})}),(0,d.jsx)(R,{imageUrl:r.editedImageUrl,className:"h-full"})]})]})]})}),(0,d.jsx)(M,{position:"top-right",richColors:!0})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},82704:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87285:(a,b,c)=>{Promise.resolve().then(c.bind(c,60181))},91645:a=>{"use strict";a.exports=require("net")},93866:(a,b,c)=>{Promise.resolve().then(c.bind(c,23392))},94735:a=>{"use strict";a.exports=require("events")},96241:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}process.env.SUPABASE_SERVICE_KEY},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,127,410,811,522,223],()=>b(b.s=39455));module.exports=c})();