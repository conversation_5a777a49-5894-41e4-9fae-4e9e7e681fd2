"use strict";(()=>{var a={};a.id=230,a.ids=[230],a.modules={261:a=>{a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42845:(a,b,c)=>{c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>w,dynamic:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190);let v=Buffer.from("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","base64");function w(){return new u.NextResponse(v,{headers:{"Content-Type":"image/x-icon","Cache-Control":"public, max-age=0, must-revalidate"}})}let x="force-static",y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/favicon.ico/route",pathname:"/favicon.ico",filename:"favicon",bundlePath:"app/favicon.ico/route"},distDir:".next",projectDir:"",resolvedPagePath:"next-metadata-route-loader?filePath=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2Fcrybaby-2%2Fapp%2Ffavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/favicon.ico/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},44870:a=>{a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[73,55],()=>b(b.s=42845));module.exports=c})();